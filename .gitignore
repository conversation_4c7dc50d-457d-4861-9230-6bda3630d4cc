# Dependencies
node_modules/
*/node_modules/

# Backend
backend/venv/
backend/__pycache__/
backend/**/__pycache__/
backend/**/*.pyc
backend/.pytest_cache/
backend/.coverage
backend/.env

# Frontend
frontend/build/
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# API Keys and Secrets
.env
.env.local
.env.*.local
secrets.enc
backend/secrets.enc

# Build outputs
dist/
build/

# Cache
.cache/
.npm/
.yarn/