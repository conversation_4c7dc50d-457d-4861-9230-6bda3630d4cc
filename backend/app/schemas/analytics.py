"""
Analytics schemas for API responses
"""
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class EngagementMetrics(BaseModel):
    views: int = 0
    likes: int = 0
    comments: int = 0
    shares: int = 0
    saves: int = 0
    clicks: int = 0
    engagement_rate: float = 0.0
    
    
class ReachMetrics(BaseModel):
    impressions: int = 0
    reach: int = 0
    unique_views: int = 0
    

class AudienceMetrics(BaseModel):
    follower_count: int = 0
    new_followers: int = 0
    unfollows: int = 0
    demographics: Optional[Dict[str, Any]] = None
    locations: Optional[Dict[str, Any]] = None
    interests: Optional[List[str]] = None


class PostAnalyticsResponse(BaseModel):
    post_id: int
    platform: str
    engagement: EngagementMetrics
    reach: ReachMetrics
    audience: AudienceMetrics
    captured_at: datetime
    
    class Config:
        from_attributes = True


class PerformanceScore(BaseModel):
    overall_score: float = Field(..., ge=0, le=100)
    engagement_score: float = Field(..., ge=0, le=100)
    reach_score: float = Field(..., ge=0, le=100)
    conversion_score: float = Field(..., ge=0, le=100)


class ContentPerformanceResponse(BaseModel):
    content_type: str
    content_category: str
    performance: PerformanceScore
    best_time: Optional[str] = None
    best_day: Optional[str] = None
    best_hashtags: Optional[List[str]] = None
    predictions: Optional[Dict[str, float]] = None
    
    class Config:
        from_attributes = True


class GrowthMetrics(BaseModel):
    follower_growth: int
    follower_growth_rate: float
    engagement_growth_rate: float
    period: str
    period_start: datetime
    period_end: datetime


class AnalyticsSummaryResponse(BaseModel):
    user_id: int
    period: str
    total_posts: int
    engagement: EngagementMetrics
    growth: GrowthMetrics
    top_posts: List[Dict[str, Any]]
    insights: List[str]
    recommendations: List[str]
    
    class Config:
        from_attributes = True


class CompetitorMetrics(BaseModel):
    username: str
    platform: str
    follower_count: int
    avg_engagement_rate: float
    content_themes: List[str]
    posting_frequency: float


class CompetitorComparisonResponse(BaseModel):
    competitor: CompetitorMetrics
    comparison: Dict[str, float]
    insights: Dict[str, List[str]]
    recommendations: List[str]
    
    class Config:
        from_attributes = True


class TrendData(BaseModel):
    name: str
    category: str
    volume: int
    growth_rate: float
    virality_score: float
    peak_time: Optional[datetime] = None
    related_hashtags: List[str]


class TrendingResponse(BaseModel):
    platform: str
    trends: List[TrendData]
    recommendations: List[Dict[str, Any]]
    
    class Config:
        from_attributes = True


class ABTestConfig(BaseModel):
    test_name: str
    test_type: str
    variant_a: Dict[str, Any]
    variant_b: Dict[str, Any]
    sample_size: int


class ABTestResultResponse(BaseModel):
    test_name: str
    winner: str
    confidence_level: float
    variant_a_performance: Dict[str, float]
    variant_b_performance: Dict[str, float]
    key_findings: List[str]
    recommendations: List[str]
    
    class Config:
        from_attributes = True


class DashboardMetrics(BaseModel):
    """Main dashboard overview metrics"""
    total_followers: int
    total_engagement: int
    avg_engagement_rate: float
    total_reach: int
    growth_rate: float
    top_performing_content: List[Dict[str, Any]]
    content_calendar: List[Dict[str, Any]]
    recent_alerts: List[Dict[str, Any]]


class PlatformBreakdown(BaseModel):
    """Platform-specific analytics"""
    platform: str
    followers: int
    engagement_rate: float
    top_posts: List[Dict[str, Any]]
    best_posting_times: List[str]
    audience_demographics: Dict[str, Any]


class AnalyticsDashboardResponse(BaseModel):
    """Complete analytics dashboard data"""
    overview: DashboardMetrics
    platforms: List[PlatformBreakdown]
    trends: List[TrendData]
    competitors: List[CompetitorMetrics]
    recommendations: List[Dict[str, Any]]
    
    class Config:
        from_attributes = True