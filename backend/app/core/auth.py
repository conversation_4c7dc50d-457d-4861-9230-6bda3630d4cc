"""
Enhanced authentication with OAuth2 and refresh tokens
"""
from datetime import datetime, timedelta
from typing import Any, Union, Optional, Dict
from jose import jwt, JWTError
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth<PERSON><PERSON><PERSON><PERSON><PERSON>earer
import secrets
import uuid

from app.core.config import settings
from app.models.user import User
# from app.core.redis_client import redis_client  # Temporarily disabled

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/token")

# Token settings
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 30


class TokenData:
    """Token payload data"""
    def __init__(self, user_id: int, email: str, token_type: str = "access"):
        self.user_id = user_id
        self.email = email
        self.token_type = token_type
        self.jti = str(uuid.uuid4())  # JWT ID for revocation


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "access"
    })
    
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: Dict[str, Any]) -> str:
    """Create JWT refresh token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "refresh"
    })
    
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


async def create_tokens(user: User) -> Dict[str, Any]:
    """Create both access and refresh tokens"""
    token_data = {
        "sub": str(user.id),
        "email": user.email,
        "username": user.username
    }
    
    access_token = create_access_token(token_data)
    refresh_token = create_refresh_token(token_data)
    
    # Store refresh token in Redis for validation
    await redis_client.cache_set(
        f"refresh_token:{user.id}:{refresh_token[-10:]}",
        {
            "user_id": user.id,
            "created_at": datetime.utcnow().isoformat()
        },
        expire=REFRESH_TOKEN_EXPIRE_DAYS * 24 * 3600
    )
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }


async def verify_token(token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        
        # Check token type
        if payload.get("type") != token_type:
            return None
        
        # Check if token is revoked (for refresh tokens)
        if token_type == "refresh":
            user_id = payload.get("sub")
            token_key = f"refresh_token:{user_id}:{token[-10:]}"
            if not await redis_client.cache_exists(token_key):
                return None
        
        return payload
    except JWTError:
        return None


async def revoke_refresh_token(user_id: int, token: str):
    """Revoke a refresh token"""
    token_key = f"refresh_token:{user_id}:{token[-10:]}"
    await redis_client.cache_delete(token_key)


async def revoke_all_refresh_tokens(user_id: int):
    """Revoke all refresh tokens for a user"""
    pattern = f"refresh_token:{user_id}:*"
    await cache_manager.invalidate_pattern(pattern)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)


async def authenticate_user(
    db: AsyncSession, 
    username: str, 
    password: str
) -> Optional[User]:
    """Authenticate user with username/email and password"""
    
    # Try to find user by email or username
    stmt = select(User).where(
        (User.email == username) | (User.username == username)
    )
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        return None
    
    if not verify_password(password, user.hashed_password):
        return None
    
    return user


# OAuth2 password flow
class OAuth2RefreshBearer(OAuth2PasswordBearer):
    """Custom OAuth2 scheme that accepts refresh tokens"""
    
    def __init__(self, tokenUrl: str, scheme_name: str = None, auto_error: bool = True):
        super().__init__(tokenUrl=tokenUrl, scheme_name=scheme_name, auto_error=auto_error)


oauth2_refresh_scheme = OAuth2RefreshBearer(tokenUrl="/api/auth/token")


async def get_current_user_from_token(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = None
) -> User:
    """Get current user from access token"""
    # Import here to avoid circular import
    if db is None:
        from app.api.dependencies import get_db
        # This will be injected by FastAPI at runtime
        raise RuntimeError("Database session not provided")
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    payload = await verify_token(token, "access")
    if not payload:
        raise credentials_exception
    
    user_id = payload.get("sub")
    if not user_id:
        raise credentials_exception
    
    # Get user from database
    stmt = select(User).where(User.id == int(user_id))
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise credentials_exception
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user_from_token)
) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


# API Key authentication for services
def create_api_key() -> str:
    """Generate a secure API key"""
    return f"sk_{secrets.token_urlsafe(32)}"


async def verify_api_key(api_key: str) -> Optional[Dict[str, Any]]:
    """Verify an API key"""
    # Redis disabled - would check cache here
    # key_data = await redis_client.cache_get(f"api_key:{api_key}")
    # if key_data:
    #     return key_data
    
    # TODO: Check database for API key
    return None


# Session management - commented out as Redis is disabled
# async def create_session(user_id: int, device_info: Dict[str, Any]) -> str:
#     """Create a new session"""
#     session_id = str(uuid.uuid4())
#     session_data = {
#         "user_id": user_id,
#         "device_info": device_info,
#         "created_at": datetime.utcnow().isoformat(),
#         "last_active": datetime.utcnow().isoformat()
#     }
#     
#     await redis_client.session_set(session_id, session_data)
#     return session_id
# 
# 
# async def get_session(session_id: str) -> Optional[Dict[str, Any]]:
#     """Get session data"""
#     return await redis_client.session_get(session_id)
# 
# 
# async def update_session_activity(session_id: str):
#     """Update session last activity time"""
#     session_data = await get_session(session_id)
#     if session_data:
#         session_data["last_active"] = datetime.utcnow().isoformat()
#         await redis_client.session_set(session_id, session_data)
# 
# 
# async def delete_session(session_id: str):
#     """Delete a session"""
#     await redis_client.session_delete(session_id)
# 
# 
# from app.core.cache import cache_manager  # Commented out