"""
WebSocket manager for real-time updates
"""
import json
from typing import Dict, List, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
import asyncio


class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}
        self.user_connections: Dict[int, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, room: str, user_id: Optional[int] = None):
        """Connect a WebSocket to a room"""
        await websocket.accept()
        
        # Add to room
        if room not in self.active_connections:
            self.active_connections[room] = []
        self.active_connections[room].append(websocket)
        
        # Add to user connections if user_id provided
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []
            self.user_connections[user_id].append(websocket)
        
        print(f"✅ WebSocket connected to room '{room}' (user: {user_id})")
    
    def disconnect(self, websocket: WebSocket, room: str, user_id: Optional[int] = None):
        """Disconnect a WebSocket from a room"""
        # Remove from room
        if room in self.active_connections:
            if websocket in self.active_connections[room]:
                self.active_connections[room].remove(websocket)
            
            # Clean up empty rooms
            if not self.active_connections[room]:
                del self.active_connections[room]
        
        # Remove from user connections
        if user_id and user_id in self.user_connections:
            if websocket in self.user_connections[user_id]:
                self.user_connections[user_id].remove(websocket)
            
            # Clean up empty user connections
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        print(f"❌ WebSocket disconnected from room '{room}' (user: {user_id})")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send a message to a specific WebSocket"""
        try:
            await websocket.send_text(json.dumps({
                **message,
                "timestamp": datetime.utcnow().isoformat()
            }))
        except Exception as e:
            print(f"Failed to send personal message: {e}")
    
    async def send_to_room(self, room: str, message: Dict[str, Any]):
        """Send a message to all WebSockets in a room"""
        if room not in self.active_connections:
            return
        
        message_data = json.dumps({
            **message,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Send to all connections in room
        disconnected = []
        for connection in self.active_connections[room]:
            try:
                await connection.send_text(message_data)
            except Exception as e:
                print(f"Failed to send to room {room}: {e}")
                disconnected.append(connection)
        
        # Clean up disconnected connections
        for connection in disconnected:
            self.active_connections[room].remove(connection)
    
    async def send_to_user(self, user_id: int, message: Dict[str, Any]):
        """Send a message to all WebSockets for a specific user"""
        if user_id not in self.user_connections:
            return
        
        message_data = json.dumps({
            **message,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Send to all user connections
        disconnected = []
        for connection in self.user_connections[user_id]:
            try:
                await connection.send_text(message_data)
            except Exception as e:
                print(f"Failed to send to user {user_id}: {e}")
                disconnected.append(connection)
        
        # Clean up disconnected connections
        for connection in disconnected:
            self.user_connections[user_id].remove(connection)
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connected WebSockets"""
        message_data = json.dumps({
            **message,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        all_connections = []
        for room_connections in self.active_connections.values():
            all_connections.extend(room_connections)
        
        # Remove duplicates
        unique_connections = list(set(all_connections))
        
        disconnected = []
        for connection in unique_connections:
            try:
                await connection.send_text(message_data)
            except Exception as e:
                print(f"Failed to broadcast: {e}")
                disconnected.append(connection)
        
        # Note: We don't clean up here as connections belong to specific rooms
    
    def get_room_count(self, room: str) -> int:
        """Get the number of connections in a room"""
        return len(self.active_connections.get(room, []))
    
    def get_user_connection_count(self, user_id: int) -> int:
        """Get the number of connections for a user"""
        return len(self.user_connections.get(user_id, []))
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        total_connections = sum(len(connections) for connections in self.active_connections.values())
        
        return {
            "total_connections": total_connections,
            "active_rooms": len(self.active_connections),
            "connected_users": len(self.user_connections),
            "rooms": {
                room: len(connections) 
                for room, connections in self.active_connections.items()
            }
        }


# Global connection manager
manager = ConnectionManager()


class ProgressTracker:
    """Tracks progress for long-running operations"""
    
    def __init__(self, user_id: int, operation_id: str, total_steps: int):
        self.user_id = user_id
        self.operation_id = operation_id
        self.total_steps = total_steps
        self.current_step = 0
        self.status = "starting"
        self.messages = []
        self.start_time = datetime.utcnow()
        self.data = {}
    
    async def update(self, step: int, message: str, status: str = "in_progress", data: Optional[Dict[str, Any]] = None):
        """Update progress and notify connected clients"""
        self.current_step = step
        self.status = status
        self.messages.append({
            "step": step,
            "message": message,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        if data:
            self.data.update(data)
        
        # Calculate progress percentage
        progress_percent = min(100, (step / self.total_steps) * 100)
        
        # Send update to user
        await manager.send_to_user(self.user_id, {
            "type": "progress_update",
            "operation_id": self.operation_id,
            "step": step,
            "total_steps": self.total_steps,
            "progress_percent": progress_percent,
            "status": status,
            "message": message,
            "data": data or {},
            "elapsed_time": (datetime.utcnow() - self.start_time).total_seconds()
        })
    
    async def complete(self, message: str = "Operation completed", data: Optional[Dict[str, Any]] = None):
        """Mark operation as complete"""
        await self.update(self.total_steps, message, "completed", data)
    
    async def error(self, message: str, error_details: Optional[str] = None):
        """Mark operation as failed"""
        error_data = {"error_details": error_details} if error_details else {}
        await self.update(self.current_step, message, "error", error_data)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get operation summary"""
        return {
            "operation_id": self.operation_id,
            "user_id": self.user_id,
            "total_steps": self.total_steps,
            "current_step": self.current_step,
            "status": self.status,
            "progress_percent": min(100, (self.current_step / self.total_steps) * 100),
            "start_time": self.start_time.isoformat(),
            "elapsed_time": (datetime.utcnow() - self.start_time).total_seconds(),
            "messages": self.messages[-5:],  # Last 5 messages
            "data": self.data
        }


# Global progress trackers
active_operations: Dict[str, ProgressTracker] = {}


def create_progress_tracker(user_id: int, operation_type: str, total_steps: int) -> ProgressTracker:
    """Create a new progress tracker"""
    operation_id = f"{operation_type}_{user_id}_{int(datetime.utcnow().timestamp())}"
    tracker = ProgressTracker(user_id, operation_id, total_steps)
    active_operations[operation_id] = tracker
    return tracker


def get_progress_tracker(operation_id: str) -> Optional[ProgressTracker]:
    """Get an existing progress tracker"""
    return active_operations.get(operation_id)


def cleanup_completed_operations():
    """Clean up completed operations (call periodically)"""
    completed = []
    for operation_id, tracker in active_operations.items():
        if tracker.status in ["completed", "error"]:
            # Keep completed operations for 1 hour
            elapsed = (datetime.utcnow() - tracker.start_time).total_seconds()
            if elapsed > 3600:  # 1 hour
                completed.append(operation_id)
    
    for operation_id in completed:
        del active_operations[operation_id]
    
    if completed:
        print(f"🧹 Cleaned up {len(completed)} completed operations")