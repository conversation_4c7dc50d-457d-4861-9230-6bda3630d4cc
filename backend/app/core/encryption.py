"""
Encryption utilities for secure storage of sensitive data
"""
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os
from typing import Optional
from app.core.config import settings


class EncryptionService:
    def __init__(self):
        self._fernet = None
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize encryption with a key derived from SECRET_KEY"""
        # Use the app's SECRET_KEY as the base for encryption
        secret_key = settings.SECRET_KEY.encode()
        
        # Generate a proper encryption key using PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'socialai-salt-v1',  # Static salt for consistency
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(secret_key))
        self._fernet = <PERSON><PERSON><PERSON>(key)
    
    def encrypt(self, plaintext: str) -> str:
        """Encrypt a string and return base64 encoded ciphertext"""
        if not plaintext:
            return ""
        
        encrypted = self._fernet.encrypt(plaintext.encode())
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def decrypt(self, ciphertext: str) -> str:
        """Decrypt a base64 encoded ciphertext and return plaintext"""
        if not ciphertext:
            return ""
        
        try:
            decoded = base64.urlsafe_b64decode(ciphertext.encode())
            decrypted = self._fernet.decrypt(decoded)
            return decrypted.decode()
        except Exception:
            # Return empty string if decryption fails
            return ""
    
    def encrypt_dict(self, data: dict) -> dict:
        """Encrypt all values in a dictionary"""
        encrypted_data = {}
        for key, value in data.items():
            if isinstance(value, str):
                encrypted_data[key] = self.encrypt(value)
            else:
                encrypted_data[key] = value
        return encrypted_data
    
    def decrypt_dict(self, data: dict) -> dict:
        """Decrypt all values in a dictionary"""
        decrypted_data = {}
        for key, value in data.items():
            if isinstance(value, str):
                decrypted_data[key] = self.decrypt(value)
            else:
                decrypted_data[key] = value
        return decrypted_data


# Singleton instance
encryption_service = EncryptionService()


# Utility functions for easier access
def encrypt_field(value: Optional[str]) -> Optional[str]:
    """Encrypt a single field value"""
    if not value:
        return None
    return encryption_service.encrypt(value)


def decrypt_field(value: Optional[str]) -> Optional[str]:
    """Decrypt a single field value"""
    if not value:
        return None
    return encryption_service.decrypt(value)