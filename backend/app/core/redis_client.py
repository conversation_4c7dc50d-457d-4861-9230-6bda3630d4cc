"""
Redis client configuration and utilities
"""
import redis.asyncio as redis
from typing import Optional, Any
import json
from app.core.config import settings


class RedisClient:
    def __init__(self):
        self._redis: Optional[redis.Redis] = None
        
    async def connect(self):
        """Initialize Redis connection"""
        self._redis = await redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True
        )
        
    async def disconnect(self):
        """Close Redis connection"""
        if self._redis:
            await self._redis.close()
    
    @property
    def redis(self) -> redis.Redis:
        """Get Redis client instance"""
        if not self._redis:
            raise RuntimeError("Redis client not initialized. Call connect() first.")
        return self._redis
    
    # Cache operations
    async def cache_set(self, key: str, value: Any, expire: int = 3600):
        """Set a value in cache with expiration"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        await self.redis.set(key, value, ex=expire)
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """Get a value from cache"""
        value = await self.redis.get(key)
        if value:
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
        return None
    
    async def cache_delete(self, key: str):
        """Delete a key from cache"""
        await self.redis.delete(key)
    
    async def cache_exists(self, key: str) -> bool:
        """Check if a key exists in cache"""
        return bool(await self.redis.exists(key))
    
    # Rate limiting operations
    async def rate_limit_check(self, key: str, limit: int, window: int) -> tuple[bool, int]:
        """
        Check rate limit for a key
        Returns: (is_allowed, remaining_requests)
        """
        current = await self.redis.incr(key)
        if current == 1:
            await self.redis.expire(key, window)
        
        remaining = max(0, limit - current)
        is_allowed = current <= limit
        
        return is_allowed, remaining
    
    async def rate_limit_reset(self, key: str):
        """Reset rate limit for a key"""
        await self.redis.delete(key)
    
    # Queue operations for background tasks
    async def queue_push(self, queue_name: str, data: dict):
        """Push data to a queue"""
        await self.redis.lpush(queue_name, json.dumps(data))
    
    async def queue_pop(self, queue_name: str, timeout: int = 0) -> Optional[dict]:
        """Pop data from a queue (blocking)"""
        result = await self.redis.brpop(queue_name, timeout=timeout)
        if result:
            return json.loads(result[1])
        return None
    
    async def queue_length(self, queue_name: str) -> int:
        """Get queue length"""
        return await self.redis.llen(queue_name)
    
    # Pub/Sub operations for real-time features
    async def publish(self, channel: str, message: dict):
        """Publish a message to a channel"""
        await self.redis.publish(channel, json.dumps(message))
    
    async def subscribe(self, *channels: str):
        """Subscribe to channels"""
        pubsub = self.redis.pubsub()
        await pubsub.subscribe(*channels)
        return pubsub
    
    # Session management
    async def session_set(self, session_id: str, data: dict, expire: int = 86400):
        """Store session data"""
        key = f"session:{session_id}"
        await self.cache_set(key, data, expire)
    
    async def session_get(self, session_id: str) -> Optional[dict]:
        """Get session data"""
        key = f"session:{session_id}"
        return await self.cache_get(key)
    
    async def session_delete(self, session_id: str):
        """Delete session"""
        key = f"session:{session_id}"
        await self.cache_delete(key)
    
    # Analytics counters
    async def increment_counter(self, key: str, amount: int = 1) -> int:
        """Increment a counter and return new value"""
        return await self.redis.incr(key, amount)
    
    async def get_counter(self, key: str) -> int:
        """Get counter value"""
        value = await self.redis.get(key)
        return int(value) if value else 0
    
    # Distributed locks
    async def acquire_lock(self, lock_name: str, timeout: int = 10) -> bool:
        """Acquire a distributed lock"""
        return bool(await self.redis.set(f"lock:{lock_name}", "1", nx=True, ex=timeout))
    
    async def release_lock(self, lock_name: str):
        """Release a distributed lock"""
        await self.redis.delete(f"lock:{lock_name}")


# Singleton instance
redis_client = RedisClient()