"""
Caching utilities and decorators
"""
from functools import wraps
from typing import Optional, Callable, Any
import hashlib
import json
import asyncio
from app.core.redis_client import redis_client


def make_cache_key(*args, **kwargs) -> str:
    """Generate a cache key from function arguments"""
    # Create a string representation of arguments
    key_parts = []
    
    # Add positional arguments
    for arg in args:
        if hasattr(arg, '__dict__'):
            # For objects, use their dict representation
            key_parts.append(str(sorted(arg.__dict__.items())))
        else:
            key_parts.append(str(arg))
    
    # Add keyword arguments
    for k, v in sorted(kwargs.items()):
        key_parts.append(f"{k}={v}")
    
    # Create hash of the key parts
    key_string = ":".join(key_parts)
    return hashlib.md5(key_string.encode()).hexdigest()


def cache_result(
    expire: int = 3600,
    prefix: str = "cache",
    key_func: Optional[Callable] = None
):
    """
    Decorator to cache function results in Redis
    
    Args:
        expire: Cache expiration time in seconds
        prefix: Cache key prefix
        key_func: Optional custom function to generate cache key
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Skip first argument if it's 'self' or a request object
                cache_args = args[1:] if args and hasattr(args[0], '__dict__') else args
                cache_key = make_cache_key(*cache_args, **kwargs)
            
            full_key = f"{prefix}:{func.__name__}:{cache_key}"
            
            # Try to get from cache
            cached = await redis_client.cache_get(full_key)
            if cached is not None:
                return cached
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Store in cache
            await redis_client.cache_set(full_key, result, expire)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For sync functions, we'll skip caching
            # (Redis client is async-only)
            return func(*args, **kwargs)
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def invalidate_cache(pattern: str):
    """
    Decorator to invalidate cache entries matching a pattern after function execution
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Execute function
            result = await func(*args, **kwargs)
            
            # Invalidate cache entries
            # Note: This requires SCAN operation which we'll implement
            # For now, we'll invalidate specific keys
            
            return result
        
        return wrapper
    
    return decorator


class CacheManager:
    """Manager for advanced caching operations"""
    
    def __init__(self):
        self.default_expire = 3600  # 1 hour
    
    async def get_or_set(
        self,
        key: str,
        factory: Callable,
        expire: Optional[int] = None
    ) -> Any:
        """Get from cache or compute and set"""
        # Try to get from cache
        value = await redis_client.cache_get(key)
        if value is not None:
            return value
        
        # Compute value
        if asyncio.iscoroutinefunction(factory):
            value = await factory()
        else:
            value = factory()
        
        # Store in cache
        await redis_client.cache_set(
            key, value, expire or self.default_expire
        )
        
        return value
    
    async def invalidate_pattern(self, pattern: str):
        """Invalidate all keys matching a pattern"""
        # Note: This is expensive operation, use with caution
        cursor = 0
        while True:
            cursor, keys = await redis_client.redis.scan(
                cursor, match=pattern, count=100
            )
            if keys:
                await redis_client.redis.delete(*keys)
            if cursor == 0:
                break
    
    async def invalidate_user_cache(self, user_id: int):
        """Invalidate all cache entries for a user"""
        patterns = [
            f"cache:*:user:{user_id}:*",
            f"cache:*:{user_id}:*",
            f"rate_limit:*:{user_id}:*"
        ]
        for pattern in patterns:
            await self.invalidate_pattern(pattern)
    
    async def get_cache_stats(self) -> dict:
        """Get cache statistics"""
        info = await redis_client.redis.info("stats")
        return {
            "hits": info.get("keyspace_hits", 0),
            "misses": info.get("keyspace_misses", 0),
            "hit_rate": (
                info.get("keyspace_hits", 0) / 
                (info.get("keyspace_hits", 0) + info.get("keyspace_misses", 1))
            ) * 100,
            "total_keys": await redis_client.redis.dbsize(),
            "memory_used": info.get("used_memory_human", "0B")
        }


# Singleton instance
cache_manager = CacheManager()


# Example usage in content generation
@cache_result(expire=1800, prefix="content")
async def get_cached_content_generation(
    topic: str,
    platform: str,
    ai_provider: str,
    user_id: int
) -> dict:
    """Cache content generation results"""
    # This would be called from the actual generation function
    pass