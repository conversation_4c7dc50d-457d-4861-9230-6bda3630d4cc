"""
Common dependencies for API routes
"""
from typing import AsyncGenerator
from fastapi import Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.database import get_db as db_get_db
from app.models.user import User
from app.core.auth import (
    get_current_user_from_token,
    get_current_active_user as auth_get_current_active_user
)

# Re-export database dependency
get_db = db_get_db

# Re-export auth dependencies with simpler names
async def get_current_user(
    user: User = Depends(get_current_user_from_token)
) -> User:
    """Get current authenticated user"""
    return user


async def get_current_active_user(
    user: User = Depends(auth_get_current_active_user)
) -> User:
    """Get current active user"""
    return user


async def get_current_active_superuser(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Get current active superuser"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="The user doesn't have enough privileges"
        )
    return current_user