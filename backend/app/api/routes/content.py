from typing import List, Any, Dict
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.api.dependencies_simple import get_db, get_current_active_user
from app.models.user import User
from app.models.content import Post, ContentTemplate, Platform as PlatformEnum
from app.schemas.content import (
    ContentGenerationRequest,
    GeneratedContent,
    ResearchRequest,
    ResearchData,
    PostCreate,
    PostUpdate,
    Post as PostSchema,
    ContentTemplateCreate,
    ContentTemplate as ContentTemplateSchema
)
from app.services.ai_service import ai_service, AIProvider
from app.services.perplexity_service import perplexity_service
from app.services.claude_service import claude_service
from app.agents.base import AgentContext
from app.agents import workflows

router = APIRouter()


@router.post("/generate/agent", response_model=Dict[str, Any])
async def generate_content_with_agents(
    request: ContentGenerationRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Generate content using multi-agent system"""
    
    print(f"🤖 Multi-agent content generation request:")
    print(f"   Topic: {request.topic}")
    print(f"   Platforms: {[p.value for p in request.platforms]}")
    print(f"   Workflow: {'trending' if request.use_trending else 'standard'}")
    
    results = {}
    
    for platform in request.platforms:
        # Create agent context
        context = AgentContext(
            user_id=current_user.id,
            session_id=f"session_{datetime.utcnow().timestamp()}",
            topic=request.topic,
            platform=platform.value,
            additional_context=request.additional_context,
            constraints={
                "ai_provider": request.ai_provider.value if request.ai_provider else None,
                "analyze_competitors": True,
                "deep_research": request.deep_research
            }
        )
        
        try:
            # Select workflow
            if request.use_trending:
                result = await workflows.generate_trending_content(context)
            elif not request.include_research:
                result = await workflows.generate_quick_content(context)
            else:
                result = await workflows.generate_standard_content(context)
            
            # Extract the final optimized content
            final_output = result.get("final_output", {})
            
            # Format response similar to original endpoint
            if final_output.get("optimized_content"):
                suggestions = []
                for var in final_output["optimized_content"][:3]:
                    suggestions.append({
                        "content": var["content"],
                        "character_count": len(var["content"]),
                        "hashtags": _extract_hashtags(var["content"]),
                        "variation_note": var.get("variation_note", ""),
                        "optimizations": var.get("optimizations", []),
                        "predicted_engagement": var.get("predicted_engagement", {})
                    })
                
                results[platform.value] = {
                    "suggestions": suggestions,
                    "best_variation": final_output.get("best_variation", {}),
                    "posting_recommendations": final_output.get("posting_recommendations", {}),
                    "optimization_insights": final_output.get("optimization_insights", []),
                    "trend_insights": result.get("results", {}).get("TrendAnalysisAgent", {}).get("output", {})
                }
            else:
                results[platform.value] = {"error": "No content generated"}
                
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"❌ Multi-agent generation error for {platform.value}:")
            print(error_details)
            results[platform.value] = {"error": str(e)}
    
    return {
        "status": "success",
        "results": results,
        "workflow_used": "trending" if request.use_trending else "standard"
    }

def _extract_hashtags(content: str) -> List[str]:
    """Extract hashtags from content"""
    import re
    hashtags = re.findall(r'#(\w+)', content)
    return hashtags

@router.post("/generate", response_model=List[GeneratedContent])
async def generate_content(
    request: ContentGenerationRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Generate content for multiple platforms"""
    
    print(f"🎯 Content generation request:")
    print(f"   Topic: {request.topic}")
    print(f"   Platforms: {[p.value for p in request.platforms]}")
    print(f"   AI Provider: {request.ai_provider}")
    print(f"   Include Research: {request.include_research}")
    
    generated_content = []
    research_data = None
    
    # Perform research if requested
    if request.include_research:
        try:
            research_data = await perplexity_service.research_topic(
                request.topic,
                request.additional_context,
                request.deep_research
            )
        except Exception as e:
            # Continue without research if it fails
            print(f"Research failed: {e}")
            research_data = None
    
    # Generate content for each platform
    for platform in request.platforms:
        try:
            suggestions_data = await ai_service.generate_content(
                request.topic,
                platform.value,
                request.ai_provider,
                research_data,
                request.additional_context
            )
            
            # Convert to PostSuggestion objects
            from app.schemas.content import PostSuggestion
            suggestions = []
            for suggestion_data in suggestions_data:
                suggestions.append(PostSuggestion(
                    content=suggestion_data["content"],
                    character_count=suggestion_data["character_count"],
                    hashtags=suggestion_data.get("hashtags"),
                    variation_note=suggestion_data.get("variation_note")
                ))
            
            generated_content.append(GeneratedContent(
                platform=platform,
                suggestions=suggestions,
                research_data=research_data
            ))
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"❌ Content generation error for {platform.value}:")
            print(error_details)
            raise HTTPException(
                status_code=500,
                detail=f"Content generation failed for {platform.value}: {str(e)}"
            )
    
    return generated_content


@router.post("/posts", response_model=PostSchema)
async def create_post(
    post_data: PostCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Create a new post"""
    
    # Convert platform string to enum
    platform_enum = PlatformEnum[post_data.platform.upper()]
    
    db_post = Post(
        user_id=current_user.id,
        topic=post_data.topic,
        content=post_data.content,
        platform=platform_enum,
        research_data=post_data.research_data
    )
    
    db.add(db_post)
    await db.commit()
    await db.refresh(db_post)
    
    return db_post


@router.get("/posts", response_model=List[PostSchema])
async def get_posts(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Get user's posts"""
    
    stmt = select(Post).where(Post.user_id == current_user.id).offset(skip).limit(limit)
    result = await db.execute(stmt)
    posts = result.scalars().all()
    
    return posts


@router.get("/posts/{post_id}", response_model=PostSchema)
async def get_post(
    post_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Get a specific post"""
    
    stmt = select(Post).where(Post.id == post_id, Post.user_id == current_user.id)
    result = await db.execute(stmt)
    post = result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    
    return post


@router.put("/posts/{post_id}", response_model=PostSchema)
async def update_post(
    post_id: int,
    update_data: PostUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Update a post"""
    
    stmt = select(Post).where(Post.id == post_id, Post.user_id == current_user.id)
    result = await db.execute(stmt)
    post = result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    
    # Update fields if provided
    if update_data.content is not None:
        post.content = update_data.content
    if update_data.status is not None:
        post.status = update_data.status
    
    post.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(post)
    
    # Broadcast update via WebSocket
    from app.core.websocket import manager
    await manager.send_to_room(
        f"content:{post_id}",
        {
            "type": "post_updated",
            "post_id": post_id,
            "updated_by": current_user.id,
            "timestamp": datetime.utcnow().isoformat()
        }
    )
    
    return post


@router.post("/variations")
async def generate_variations(
    post_id: int,
    count: int = 3,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Generate variations of existing content"""
    
    # Get the original post
    stmt = select(Post).where(Post.id == post_id, Post.user_id == current_user.id)
    result = await db.execute(stmt)
    post = result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    
    try:
        # Handle platform value safely for both enum cases
        platform_value = post.platform.value.lower() if hasattr(post.platform, 'value') else str(post.platform).lower()
        
        # Use user's API key if available
        user_claude_key = current_user.anthropic_api_key
        if user_claude_key:
            user_claude_service = type(claude_service)(user_claude_key)
            variations = await user_claude_service.generate_variations(
                post.content,
                platform_value,
                count
            )
        else:
            variations = await claude_service.generate_variations(
                post.content,
                platform_value,
                count
            )
        
        return {
            "original_content": post.content,
            "variations": variations
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Variation generation failed: {str(e)}"
        )


@router.post("/templates", response_model=ContentTemplateSchema)
async def create_template(
    template_data: ContentTemplateCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Create a content template"""
    
    # Convert platform string to enum
    platform_enum = PlatformEnum[template_data.platform.upper()]
    
    db_template = ContentTemplate(
        user_id=current_user.id,
        name=template_data.name,
        description=template_data.description,
        platform=platform_enum,
        template=template_data.template
    )
    
    db.add(db_template)
    await db.commit()
    await db.refresh(db_template)
    
    return db_template


@router.get("/templates", response_model=List[ContentTemplateSchema])
async def get_templates(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Get user's content templates"""
    
    stmt = select(ContentTemplate).where(ContentTemplate.user_id == current_user.id)
    result = await db.execute(stmt)
    templates = result.scalars().all()
    
    return templates


@router.get("/trending-topics")
async def get_trending_topics(
    category: str = None,
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Get trending topics for content inspiration"""
    
    try:
        # Use user's API key if available
        user_perplexity_key = current_user.perplexity_api_key
        if user_perplexity_key:
            user_perplexity_service = type(perplexity_service)(user_perplexity_key)
            topics = await user_perplexity_service.get_trending_topics(category)
        else:
            topics = await perplexity_service.get_trending_topics(category)
        
        return {"topics": topics}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch trending topics: {str(e)}"
        )


@router.post("/research", response_model=ResearchData)
async def research_topic(
    request: ResearchRequest,
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Research a topic using Perplexity API"""
    
    try:
        # Create progress tracker for deep research
        progress_tracker = None
        if request.deep_research:
            from app.core.websocket import create_progress_tracker
            progress_tracker = create_progress_tracker(
                user_id=current_user.id,
                operation_type="deep_research",
                total_steps=7  # 7 steps for deep research workflow
            )
            await progress_tracker.update(0, f"Initializing deep research for '{request.topic}'")
        
        # Use user's API key if available, otherwise use default
        user_perplexity_key = current_user.perplexity_api_key
        if user_perplexity_key:
            user_perplexity_service = type(perplexity_service)(user_perplexity_key)
            research_data = await user_perplexity_service.research_topic(
                request.topic,
                request.additional_context,
                request.deep_research,
                progress_tracker
            )
        else:
            research_data = await perplexity_service.research_topic(
                request.topic,
                request.additional_context,
                request.deep_research,
                progress_tracker
            )
        
        # Add operation_id to response for deep research
        if request.deep_research and progress_tracker:
            research_data["operation_id"] = progress_tracker.operation_id
        
        return research_data
        
    except Exception as e:
        if progress_tracker:
            await progress_tracker.error(f"Research failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Research failed: {str(e)}"
        )


@router.get("/ai-providers")
async def get_ai_providers(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Get available AI providers and their information"""
    
    return {
        "available_providers": ai_service.get_available_providers(),
        "provider_info": ai_service.get_provider_info()
    }