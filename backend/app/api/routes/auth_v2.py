"""
Enhanced authentication endpoints with OAuth2 and refresh tokens
"""
from datetime import timed<PERSON><PERSON>
from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, EmailStr

from app.api.dependencies import get_db
from app.core.config import settings
from app.core.auth import (
    create_tokens, verify_token, revoke_refresh_token,
    revoke_all_refresh_tokens, authenticate_user,
    get_current_active_user, get_password_hash,
    create_api_key, create_session, get_session,
    update_session_activity, delete_session
)
from app.models.user import User
from app.schemas.user import UserCreate, User as UserSchema


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class LoginRequest(BaseModel):
    username: str
    password: str
    remember_me: bool = False
    device_info: Optional[dict] = None


class APIKeyResponse(BaseModel):
    api_key: str
    created_at: str


class RevokeTokenRequest(BaseModel):
    token: str


router = APIRouter()


@router.post("/register", response_model=UserSchema)
async def register(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Register a new user with enhanced security"""
    
    # Check if user already exists
    stmt = select(User).where(
        (User.email == user_data.email) | (User.username == user_data.username)
    )
    result = await db.execute(stmt)
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
        if existing_user.email == user_data.email:
            raise HTTPException(
                status_code=400,
                detail="User with this email already exists"
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="Username already taken"
            )
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    
    db_user = User(
        email=user_data.email,
        username=user_data.username,
        hashed_password=hashed_password,
        is_active=True,
        is_superuser=False
    )
    
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    
    return db_user


@router.post("/token", response_model=TokenResponse)
async def login_for_tokens(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """OAuth2 compatible token endpoint"""
    
    user = await authenticate_user(db, form_data.username, form_data.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    # Create tokens
    tokens = await create_tokens(user)
    
    return TokenResponse(**tokens)


@router.post("/login", response_model=TokenResponse)
async def login(
    login_data: LoginRequest,
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Enhanced login endpoint with session support"""
    
    user = await authenticate_user(db, login_data.username, login_data.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    # Create tokens
    tokens = await create_tokens(user)
    
    # Create session if device info provided
    if login_data.device_info:
        device_info = {
            **login_data.device_info,
            "ip_address": request.client.host if request.client else "unknown"
        }
        session_id = await create_session(user.id, device_info)
        tokens["session_id"] = session_id
    
    return TokenResponse(**tokens)


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Refresh access token using refresh token"""
    
    # Verify refresh token
    payload = await verify_token(refresh_data.refresh_token, "refresh")
    
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Get user
    user_id = payload.get("sub")
    stmt = select(User).where(User.id == int(user_id))
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Revoke old refresh token
    await revoke_refresh_token(user.id, refresh_data.refresh_token)
    
    # Create new tokens
    tokens = await create_tokens(user)
    
    return TokenResponse(**tokens)


@router.post("/logout")
async def logout(
    token_data: RevokeTokenRequest,
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Logout and revoke refresh token"""
    
    # Revoke the specific refresh token
    await revoke_refresh_token(current_user.id, token_data.token)
    
    return {"message": "Successfully logged out"}


@router.post("/logout-all")
async def logout_all_devices(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Logout from all devices by revoking all refresh tokens"""
    
    await revoke_all_refresh_tokens(current_user.id)
    
    return {"message": "Successfully logged out from all devices"}


@router.get("/me", response_model=UserSchema)
async def get_current_user(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Get current user information"""
    return current_user


@router.post("/api-key", response_model=APIKeyResponse)
async def generate_api_key(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Generate a new API key for the user"""
    
    # Generate API key
    api_key = create_api_key()
    
    # Store API key (you might want to store this in the database)
    from app.core.redis_client import redis_client
    await redis_client.cache_set(
        f"api_key:{api_key}",
        {
            "user_id": current_user.id,
            "created_at": datetime.utcnow().isoformat()
        },
        expire=365 * 24 * 3600  # 1 year
    )
    
    return APIKeyResponse(
        api_key=api_key,
        created_at=datetime.utcnow().isoformat()
    )


@router.get("/sessions")
async def get_user_sessions(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Get all active sessions for the current user"""
    
    # TODO: Implement session listing
    # This would require storing sessions with user ID indexing
    
    return {"sessions": []}


@router.delete("/sessions/{session_id}")
async def revoke_session(
    session_id: str,
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Revoke a specific session"""
    
    session = await get_session(session_id)
    
    if not session or session.get("user_id") != current_user.id:
        raise HTTPException(
            status_code=404,
            detail="Session not found"
        )
    
    await delete_session(session_id)
    
    return {"message": "Session revoked successfully"}


@router.post("/verify-token")
async def verify_access_token(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Verify if the current access token is valid"""
    
    return {
        "valid": True,
        "user_id": current_user.id,
        "email": current_user.email
    }


from datetime import datetime