"""
Analytics API routes
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies_simple import get_db, get_current_active_user
from app.models.user import User
from app.schemas.analytics import (
    PostAnalyticsResponse, AnalyticsSummaryResponse,
    CompetitorComparisonResponse, TrendingResponse,
    ABTestConfig, ABTestResultResponse,
    AnalyticsDashboardResponse
)
from app.services.analytics_service import analytics_service

router = APIRouter()


@router.get("/dashboard", response_model=AnalyticsDashboardResponse)
async def get_analytics_dashboard(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get complete analytics dashboard for the current user"""
    dashboard_data = await analytics_service.get_dashboard_data(db, current_user.id)
    return dashboard_data


@router.get("/posts/{post_id}", response_model=PostAnalyticsResponse)
async def get_post_analytics(
    post_id: int,
    platform: Optional[str] = Query(None, description="Filter by platform"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get analytics for a specific post"""
    # Verify post ownership
    from app.models.content import Post
    from sqlalchemy import select
    
    post_query = select(Post).where(Post.id == post_id, Post.user_id == current_user.id)
    post_result = await db.execute(post_query)
    post = post_result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    
    analytics = await analytics_service.get_post_analytics(db, post_id, platform)
    if not analytics:
        raise HTTPException(status_code=404, detail="Analytics not found for this post")
    
    return PostAnalyticsResponse(
        post_id=analytics.post_id,
        platform=analytics.platform,
        engagement={
            "views": analytics.views,
            "likes": analytics.likes,
            "comments": analytics.comments,
            "shares": analytics.shares,
            "saves": analytics.saves,
            "clicks": analytics.clicks,
            "engagement_rate": analytics.engagement_rate
        },
        reach={
            "impressions": analytics.impressions,
            "reach": analytics.reach,
            "unique_views": analytics.unique_views
        },
        audience={
            "follower_count": analytics.follower_count,
            "new_followers": analytics.new_followers,
            "unfollows": analytics.unfollows,
            "demographics": analytics.audience_demographics,
            "locations": analytics.audience_locations,
            "interests": analytics.audience_interests
        },
        captured_at=analytics.captured_at
    )


@router.get("/summary", response_model=AnalyticsSummaryResponse)
async def get_analytics_summary(
    period: str = Query("weekly", regex="^(daily|weekly|monthly)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get analytics summary for the current user"""
    summary = await analytics_service.get_user_summary(db, current_user.id, period)
    
    return AnalyticsSummaryResponse(
        user_id=summary.user_id,
        period=summary.period,
        total_posts=summary.total_posts,
        engagement={
            "views": summary.total_views,
            "likes": summary.total_likes,
            "comments": summary.total_comments,
            "shares": summary.total_shares,
            "saves": 0,  # Not tracked in summary yet
            "clicks": summary.total_clicks,
            "engagement_rate": summary.avg_engagement_rate
        },
        growth={
            "follower_growth": summary.follower_growth or 0,
            "follower_growth_rate": summary.follower_growth_rate or 0.0,
            "engagement_growth_rate": summary.engagement_growth_rate or 0.0,
            "period": summary.period,
            "period_start": summary.period_start,
            "period_end": summary.period_end
        },
        top_posts=summary.top_posts or [],
        insights=summary.insights or [],
        recommendations=summary.recommendations or []
    )


@router.get("/trends/{platform}", response_model=TrendingResponse)
async def get_trending_content(
    platform: str,
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get trending content for a specific platform"""
    trends = await analytics_service.get_trending_content(db, platform, limit)
    
    trend_data = []
    recommendations = []
    
    for trend in trends:
        trend_data.append({
            "name": trend.trend_name,
            "category": trend.trend_category or "general",
            "volume": trend.volume,
            "growth_rate": trend.growth_rate,
            "virality_score": trend.virality_score,
            "peak_time": trend.peak_time,
            "related_hashtags": trend.related_hashtags or []
        })
        
        # Add recommendations based on trends
        if trend.virality_score > 0.8:
            recommendations.append({
                "trend": trend.trend_name,
                "action": f"Create content about {trend.trend_name} while it's hot!",
                "urgency": "high"
            })
    
    return TrendingResponse(
        platform=platform,
        trends=trend_data,
        recommendations=recommendations
    )


@router.post("/competitors/analyze", response_model=CompetitorComparisonResponse)
async def analyze_competitor(
    competitor_username: str,
    platform: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Analyze a competitor's performance"""
    analysis = await analytics_service.analyze_competitors(
        db, current_user.id, competitor_username, platform
    )
    
    return CompetitorComparisonResponse(
        competitor={
            "username": analysis.competitor_username,
            "platform": analysis.platform,
            "follower_count": analysis.follower_count,
            "avg_engagement_rate": analysis.avg_engagement_rate,
            "content_themes": analysis.content_themes or [],
            "posting_frequency": analysis.avg_posts_per_day or 0.0
        },
        comparison={
            "engagement": analysis.engagement_comparison or 0.0,
            "growth": analysis.growth_comparison or 0.0,
            "reach": analysis.reach_comparison or 0.0
        },
        insights={
            "strengths": analysis.strengths or [],
            "weaknesses": analysis.weaknesses or [],
            "opportunities": analysis.opportunities or []
        },
        recommendations=analysis.recommendations or []
    )


@router.post("/ab-test", response_model=ABTestResultResponse)
async def create_ab_test(
    test_config: ABTestConfig,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create and run an A/B test"""
    # This would typically run the test over time
    # For now, return mock results
    from app.models.analytics import ABTestResult
    
    test_result = ABTestResult(
        user_id=current_user.id,
        test_name=test_config.test_name,
        test_type=test_config.test_type,
        variant_a=test_config.variant_a,
        variant_b=test_config.variant_b,
        sample_size=test_config.sample_size,
        variant_a_performance={
            "engagement_rate": 3.2,
            "click_rate": 1.5,
            "conversion_rate": 0.8
        },
        variant_b_performance={
            "engagement_rate": 4.1,
            "click_rate": 2.3,
            "conversion_rate": 1.2
        },
        winner="variant_b",
        confidence_level=0.95,
        statistical_significance=0.03,
        key_findings=[
            "Variant B showed 28% higher engagement",
            "Click-through rate improved by 53%",
            "Conversion rate increased by 50%"
        ],
        recommendations=[
            "Implement Variant B strategy across all content",
            "Test similar variations with other content types",
            "Monitor performance for 2 weeks to confirm results"
        ]
    )
    
    db.add(test_result)
    await db.commit()
    await db.refresh(test_result)
    
    return ABTestResultResponse(
        test_name=test_result.test_name,
        winner=test_result.winner,
        confidence_level=test_result.confidence_level,
        variant_a_performance=test_result.variant_a_performance,
        variant_b_performance=test_result.variant_b_performance,
        key_findings=test_result.key_findings,
        recommendations=test_result.recommendations
    )


@router.get("/export")
async def export_analytics(
    format: str = Query("csv", regex="^(csv|excel|pdf)$"),
    period: str = Query("weekly", regex="^(daily|weekly|monthly)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Export analytics data in various formats"""
    # This would generate and return a file
    # For now, return a mock response
    return {
        "message": f"Analytics export in {format} format for {period} period",
        "download_url": f"/api/analytics/download/{current_user.id}_{period}.{format}"
    }