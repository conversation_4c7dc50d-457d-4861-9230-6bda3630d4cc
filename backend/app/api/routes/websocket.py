"""
WebSocket routes for real-time updates
"""
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from typing import Optional
import json

from app.core.websocket import manager, get_progress_tracker, active_operations
from app.api.dependencies_simple import get_current_user_websocket


router = APIRouter()


@router.websocket("/ws/{room}")
async def websocket_endpoint(
    websocket: WebSocket,
    room: str,
    token: Optional[str] = Query(None)
):
    """WebSocket endpoint for real-time updates"""
    
    # Authenticate user if token provided
    user_id = None
    if token:
        try:
            user = await get_current_user_websocket(token)
            user_id = user.id
        except Exception as e:
            await websocket.close(code=1008, reason="Authentication failed")
            return
    
    # Connect to room
    await manager.connect(websocket, room, user_id)
    
    try:
        while True:
            # Listen for messages from client
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await handle_websocket_message(websocket, room, user_id, message)
            except json.JSONDecodeError:
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format"
                }, websocket)
            except Exception as e:
                await manager.send_personal_message({
                    "type": "error",
                    "message": f"Error processing message: {str(e)}"
                }, websocket)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, room, user_id)


async def handle_websocket_message(
    websocket: WebSocket,
    room: str,
    user_id: Optional[int],
    message: dict
):
    """Handle incoming WebSocket messages"""
    
    message_type = message.get("type")
    
    if message_type == "ping":
        await manager.send_personal_message({
            "type": "pong",
            "message": "Connection alive"
        }, websocket)
    
    elif message_type == "join_room":
        new_room = message.get("room")
        if new_room:
            # Note: This is a simplified implementation
            # In production, you'd want to handle room switching properly
            await manager.send_personal_message({
                "type": "room_joined",
                "room": new_room
            }, websocket)
    
    elif message_type == "get_progress":
        operation_id = message.get("operation_id")
        if operation_id:
            tracker = get_progress_tracker(operation_id)
            if tracker:
                await manager.send_personal_message({
                    "type": "progress_status",
                    **tracker.get_summary()
                }, websocket)
            else:
                await manager.send_personal_message({
                    "type": "error",
                    "message": f"Operation {operation_id} not found"
                }, websocket)
    
    elif message_type == "list_operations":
        if user_id:
            user_operations = {
                op_id: tracker.get_summary()
                for op_id, tracker in active_operations.items()
                if tracker.user_id == user_id
            }
            await manager.send_personal_message({
                "type": "operations_list",
                "operations": user_operations
            }, websocket)
    
    elif message_type == "subscribe_to_operation":
        operation_id = message.get("operation_id")
        if operation_id and user_id:
            # Add user to operation-specific room
            operation_room = f"operation:{operation_id}"
            await manager.connect(websocket, operation_room, user_id)
            await manager.send_personal_message({
                "type": "subscribed",
                "operation_id": operation_id
            }, websocket)
    
    else:
        await manager.send_personal_message({
            "type": "error",
            "message": f"Unknown message type: {message_type}"
        }, websocket)


@router.get("/ws/stats")
async def get_websocket_stats():
    """Get WebSocket connection statistics"""
    return manager.get_stats()


@router.get("/ws/operations")
async def get_active_operations():
    """Get all active operations (admin only)"""
    return {
        "active_operations": len(active_operations),
        "operations": {
            op_id: {
                "user_id": tracker.user_id,
                "status": tracker.status,
                "progress_percent": min(100, (tracker.current_step / tracker.total_steps) * 100),
                "elapsed_time": tracker.get_summary()["elapsed_time"]
            }
            for op_id, tracker in active_operations.items()
        }
    }