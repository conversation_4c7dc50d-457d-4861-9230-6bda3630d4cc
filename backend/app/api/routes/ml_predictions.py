"""
ML predictions API routes
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.api.dependencies_simple import get_db, get_current_active_user
from app.models.user import User
from app.services.ml_service import ml_service

router = APIRouter()


class ContentPredictionRequest(BaseModel):
    content: str
    platform: str


class ContentPredictionResponse(BaseModel):
    predicted_engagement_rate: float
    predicted_reach: int
    confidence_score: float
    suggestions: List[str]
    features: dict


class TrainingStatusResponse(BaseModel):
    status: str
    message: str


@router.post("/predict", response_model=ContentPredictionResponse)
async def predict_content_performance(
    request: ContentPredictionRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Predict content performance before posting"""
    # Extract features
    features = await ml_service.extract_features(request.content, request.platform)
    
    # Make prediction
    prediction = await ml_service.predict_performance(
        request.content,
        request.platform,
        current_user.id
    )
    
    # Generate suggestions
    suggestions = await ml_service.generate_optimization_suggestions(
        request.content,
        request.platform,
        prediction
    )
    
    return ContentPredictionResponse(
        predicted_engagement_rate=prediction['predicted_engagement_rate'],
        predicted_reach=prediction['predicted_reach'],
        confidence_score=prediction['confidence_score'],
        suggestions=suggestions,
        features=features
    )


@router.post("/train", response_model=TrainingStatusResponse)
async def train_ml_models(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Train ML models on user's historical data"""
    success = await ml_service.train_models(db, current_user.id)
    
    if success:
        return TrainingStatusResponse(
            status="success",
            message="Models trained successfully on your historical data"
        )
    else:
        return TrainingStatusResponse(
            status="insufficient_data",
            message="Need at least 10 posts with analytics data to train models"
        )


@router.get("/performance-analysis")
async def get_performance_analysis(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get detailed analysis of content performance patterns"""
    analysis = await ml_service.analyze_content_elements(db, current_user.id)
    
    if not analysis:
        raise HTTPException(
            status_code=404,
            detail="No performance data available yet"
        )
    
    return {
        "best_time": analysis.best_time,
        "best_day": analysis.best_day,
        "best_hashtags": analysis.best_hashtags,
        "optimal_content_length": analysis.best_content_length,
        "performance_scores": {
            "overall": analysis.overall_score,
            "engagement": analysis.engagement_score,
            "reach": analysis.reach_score,
            "conversion": analysis.conversion_score
        }
    }


@router.post("/optimize")
async def optimize_content(
    request: ContentPredictionRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Get AI-powered content optimization suggestions"""
    # Get current prediction
    current_prediction = await ml_service.predict_performance(
        request.content,
        request.platform,
        current_user.id
    )
    
    # Generate optimization suggestions
    suggestions = await ml_service.generate_optimization_suggestions(
        request.content,
        request.platform,
        current_prediction
    )
    
    # Use AI to rewrite content with optimizations
    from app.services.ai_service import ai_service
    
    optimized_content = await ai_service.generate_content(
        f"Optimize this social media post for {request.platform}. Apply these suggestions: {', '.join(suggestions[:3])}. Original post: {request.content}",
        request.platform,
        provider="claude"
    )
    
    # Predict performance of optimized content
    optimized_prediction = await ml_service.predict_performance(
        optimized_content,
        request.platform,
        current_user.id
    )
    
    return {
        "original_content": request.content,
        "optimized_content": optimized_content,
        "original_prediction": current_prediction,
        "optimized_prediction": optimized_prediction,
        "improvement": {
            "engagement_rate": (optimized_prediction['predicted_engagement_rate'] - current_prediction['predicted_engagement_rate']) / current_prediction['predicted_engagement_rate'] * 100,
            "reach": (optimized_prediction['predicted_reach'] - current_prediction['predicted_reach']) / current_prediction['predicted_reach'] * 100
        },
        "applied_suggestions": suggestions[:3]
    }