"""
Rate limiting middleware for FastAPI
"""
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable, Optional
import time
from app.core.redis_client import redis_client
from app.core.config import settings


class RateLimitMiddleware(BaseHTTPMiddleware):
    def __init__(
        self,
        app,
        calls: int = settings.RATE_LIMIT_PER_MINUTE,
        period: int = 60,
        scope: str = "global"
    ):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.scope = scope
    
    async def dispatch(self, request: Request, call_next: Callable):
        # Skip rate limiting for health checks and docs
        if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        # Get client identifier
        client_id = self._get_client_id(request)
        
        # Check rate limit
        key = f"rate_limit:{self.scope}:{client_id}:{request.url.path}"
        is_allowed, remaining = await redis_client.rate_limit_check(
            key, self.calls, self.period
        )
        
        if not is_allowed:
            return JSONResponse(
                status_code=429,
                content={
                    "detail": "Rate limit exceeded",
                    "retry_after": self.period
                },
                headers={
                    "X-RateLimit-Limit": str(self.calls),
                    "X-RateLimit-Remaining": "0",
                    "X-RateLimit-Reset": str(int(time.time()) + self.period),
                    "Retry-After": str(self.period)
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        response.headers["X-RateLimit-Limit"] = str(self.calls)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + self.period)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier from request"""
        # Try to get authenticated user ID from request state
        if hasattr(request.state, "user_id"):
            return f"user:{request.state.user_id}"
        
        # Fall back to IP address
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            client_ip = forwarded.split(",")[0].strip()
        else:
            client_ip = request.client.host if request.client else "unknown"
        
        return f"ip:{client_ip}"


class APIKeyRateLimiter:
    """Rate limiter for API key usage"""
    
    def __init__(self, provider: str, calls_per_minute: int = 10, calls_per_hour: int = 100):
        self.provider = provider
        self.calls_per_minute = calls_per_minute
        self.calls_per_hour = calls_per_hour
    
    async def check_and_update(self, user_id: int) -> bool:
        """Check if API call is allowed and update counters"""
        minute_key = f"api_rate:{self.provider}:{user_id}:minute"
        hour_key = f"api_rate:{self.provider}:{user_id}:hour"
        
        # Check minute limit
        minute_allowed, _ = await redis_client.rate_limit_check(
            minute_key, self.calls_per_minute, 60
        )
        
        if not minute_allowed:
            return False
        
        # Check hour limit
        hour_allowed, _ = await redis_client.rate_limit_check(
            hour_key, self.calls_per_hour, 3600
        )
        
        return hour_allowed
    
    async def get_usage(self, user_id: int) -> dict:
        """Get current usage statistics"""
        minute_key = f"api_rate:{self.provider}:{user_id}:minute"
        hour_key = f"api_rate:{self.provider}:{user_id}:hour"
        
        minute_count = await redis_client.get_counter(minute_key)
        hour_count = await redis_client.get_counter(hour_key)
        
        return {
            "provider": self.provider,
            "minute": {
                "used": minute_count,
                "limit": self.calls_per_minute,
                "remaining": max(0, self.calls_per_minute - minute_count)
            },
            "hour": {
                "used": hour_count,
                "limit": self.calls_per_hour,
                "remaining": max(0, self.calls_per_hour - hour_count)
            }
        }


# Create rate limiters for each AI provider
claude_rate_limiter = APIKeyRateLimiter("claude", calls_per_minute=5, calls_per_hour=50)
openai_rate_limiter = APIKeyRateLimiter("openai", calls_per_minute=20, calls_per_hour=200)
perplexity_rate_limiter = APIKeyRateLimiter("perplexity", calls_per_minute=10, calls_per_hour=100)
gemini_rate_limiter = APIKeyRateLimiter("gemini", calls_per_minute=60, calls_per_hour=1000)