"""
Migration script to encrypt existing API keys in the database
"""
import asyncio
from sqlalchemy import select, text
from app.models.database import AsyncSessionLocal, engine
from app.models.user import User
from app.core.encryption import encrypt_field
import sys


async def encrypt_existing_keys():
    """Encrypt any existing plain text API keys"""
    async with AsyncSessionLocal() as session:
        try:
            # Check if we need to migrate by trying to read users
            stmt = select(User)
            result = await session.execute(stmt)
            users = result.scalars().all()
            
            migrated_count = 0
            
            for user in users:
                needs_update = False
                
                # Check each field and encrypt if it looks like plain text
                fields_to_check = [
                    'anthropic_api_key',
                    'perplexity_api_key', 
                    'twitter_access_token',
                    'twitter_access_token_secret',
                    'linkedin_access_token'
                ]
                
                for field in fields_to_check:
                    value = getattr(user, field)
                    if value and not value.startswith('gAAAAA'):  # Fernet encrypted data starts with this
                        # This is plain text, encrypt it
                        encrypted_value = encrypt_field(value)
                        
                        # Update the user directly with raw SQL to avoid ORM encryption
                        await session.execute(
                            text(f"UPDATE users SET {field} = :value WHERE id = :user_id"),
                            {"value": encrypted_value, "user_id": user.id}
                        )
                        needs_update = True
                
                if needs_update:
                    migrated_count += 1
            
            await session.commit()
            print(f"✅ Encrypted API keys for {migrated_count} users")
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            await session.rollback()
            sys.exit(1)


if __name__ == "__main__":
    print("🔒 Starting API key encryption migration...")
    asyncio.run(encrypt_existing_keys())
    print("✅ Migration completed successfully!")