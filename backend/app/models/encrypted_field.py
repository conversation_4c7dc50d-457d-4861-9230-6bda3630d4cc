"""
Custom SQLAlchemy field type for encrypted data
"""
from sqlalchemy import TypeDecorator, String
from app.core.encryption import encrypt_field, decrypt_field


class EncryptedString(TypeDecorator):
    """An SQLAlchemy type that encrypts/decrypts strings automatically"""
    
    impl = String
    cache_ok = True
    
    def process_bind_param(self, value, dialect):
        """Encrypt value before storing in database"""
        if value is not None:
            return encrypt_field(value)
        return value
    
    def process_result_value(self, value, dialect):
        """Decrypt value when loading from database"""
        if value is not None:
            return decrypt_field(value)
        return value
    
    def process_literal_param(self, value, dialect):
        """Handle literal params (used in debugging)"""
        return value


def encrypted_string_field(length=None):
    """Factory function to create encrypted string fields"""
    if length:
        return EncryptedString(length=length)
    return EncryptedString()