"""
Analytics models for tracking engagement and performance
"""
from sqlalchemy import Column, Integer, String, DateTime, Float, ForeignKey, JSON, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.models.database import Base


class PostAnalytics(Base):
    __tablename__ = "post_analytics"
    
    id = Column(Integer, primary_key=True, index=True)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=False)
    platform = Column(String, nullable=False)
    
    # Engagement metrics
    views = Column(Integer, default=0)
    likes = Column(Integer, default=0)
    comments = Column(Integer, default=0)
    shares = Column(Integer, default=0)
    saves = Column(Integer, default=0)
    clicks = Column(Integer, default=0)
    
    # Reach metrics
    impressions = Column(Integer, default=0)
    reach = Column(Integer, default=0)
    unique_views = Column(Integer, default=0)
    
    # Audience metrics
    follower_count = Column(Integer, default=0)
    new_followers = Column(Integer, default=0)
    unfollows = Column(Integer, default=0)
    
    # Performance metrics
    engagement_rate = Column(Float, default=0.0)
    click_through_rate = Column(Float, default=0.0)
    conversion_rate = Column(Float, default=0.0)
    
    # Demographics (JSON)
    audience_demographics = Column(JSON)
    audience_locations = Column(JSON)
    audience_interests = Column(JSON)
    
    # Timestamps
    captured_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    post = relationship("Post", back_populates="analytics")


class ContentPerformance(Base):
    __tablename__ = "content_performance"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Content details
    content_type = Column(String)  # text, image, video, carousel
    content_category = Column(String)  # educational, promotional, entertainment
    content_topic = Column(String)
    hashtags = Column(JSON)
    
    # Performance scores
    overall_score = Column(Float, default=0.0)
    engagement_score = Column(Float, default=0.0)
    reach_score = Column(Float, default=0.0)
    conversion_score = Column(Float, default=0.0)
    
    # Best performing elements
    best_time = Column(String)
    best_day = Column(String)
    best_hashtags = Column(JSON)
    best_content_length = Column(Integer)
    
    # ML predictions
    predicted_engagement = Column(Float)
    predicted_reach = Column(Float)
    confidence_score = Column(Float)
    
    # Timestamps
    analyzed_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")


class UserAnalyticsSummary(Base):
    __tablename__ = "user_analytics_summary"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    period = Column(String)  # daily, weekly, monthly
    period_start = Column(DateTime(timezone=True))
    period_end = Column(DateTime(timezone=True))
    
    # Aggregate metrics
    total_posts = Column(Integer, default=0)
    total_views = Column(Integer, default=0)
    total_likes = Column(Integer, default=0)
    total_comments = Column(Integer, default=0)
    total_shares = Column(Integer, default=0)
    total_clicks = Column(Integer, default=0)
    
    # Growth metrics
    follower_growth = Column(Integer, default=0)
    follower_growth_rate = Column(Float, default=0.0)
    engagement_growth_rate = Column(Float, default=0.0)
    
    # Average metrics
    avg_engagement_rate = Column(Float, default=0.0)
    avg_reach = Column(Float, default=0.0)
    avg_impressions = Column(Float, default=0.0)
    
    # Top content
    top_posts = Column(JSON)  # Array of post IDs with metrics
    top_hashtags = Column(JSON)
    top_content_types = Column(JSON)
    
    # Insights
    insights = Column(JSON)  # AI-generated insights
    recommendations = Column(JSON)  # AI-generated recommendations
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")


class CompetitorAnalysis(Base):
    __tablename__ = "competitor_analysis"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    competitor_username = Column(String, nullable=False)
    platform = Column(String, nullable=False)
    
    # Competitor metrics
    follower_count = Column(Integer)
    following_count = Column(Integer)
    post_count = Column(Integer)
    avg_engagement_rate = Column(Float)
    avg_posts_per_day = Column(Float)
    
    # Content analysis
    content_themes = Column(JSON)
    posting_schedule = Column(JSON)
    hashtag_strategy = Column(JSON)
    content_types = Column(JSON)
    
    # Performance comparison
    engagement_comparison = Column(Float)  # Percentage difference
    growth_comparison = Column(Float)
    reach_comparison = Column(Float)
    
    # Insights
    strengths = Column(JSON)
    weaknesses = Column(JSON)
    opportunities = Column(JSON)
    recommendations = Column(JSON)
    
    # Timestamps
    analyzed_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")


class TrendAnalysis(Base):
    __tablename__ = "trend_analysis"
    
    id = Column(Integer, primary_key=True, index=True)
    platform = Column(String, nullable=False)
    trend_type = Column(String)  # hashtag, topic, format, audio
    
    # Trend details
    trend_name = Column(String, nullable=False)
    trend_description = Column(Text)
    trend_category = Column(String)
    
    # Metrics
    volume = Column(Integer)  # Number of posts
    growth_rate = Column(Float)
    engagement_rate = Column(Float)
    virality_score = Column(Float)
    
    # Predictions
    peak_time = Column(DateTime(timezone=True))
    expected_duration = Column(Integer)  # Days
    confidence_score = Column(Float)
    
    # Related data
    related_hashtags = Column(JSON)
    related_topics = Column(JSON)
    example_posts = Column(JSON)
    
    # Timestamps
    discovered_at = Column(DateTime(timezone=True), server_default=func.now())
    last_updated = Column(DateTime(timezone=True), onupdate=func.now())
    
    
class ABTestResult(Base):
    __tablename__ = "ab_test_results"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    test_name = Column(String, nullable=False)
    test_type = Column(String)  # content, timing, hashtags, etc.
    
    # Test configuration
    variant_a = Column(JSON)
    variant_b = Column(JSON)
    sample_size = Column(Integer)
    
    # Results
    variant_a_performance = Column(JSON)
    variant_b_performance = Column(JSON)
    winner = Column(String)
    confidence_level = Column(Float)
    statistical_significance = Column(Float)
    
    # Insights
    key_findings = Column(JSON)
    recommendations = Column(JSON)
    
    # Timestamps
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")