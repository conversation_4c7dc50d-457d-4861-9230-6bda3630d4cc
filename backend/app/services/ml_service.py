"""
Machine Learning service for content performance prediction
"""
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import json
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import joblib
import os

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from app.models.content import Post
from app.models.analytics import PostAnalytics, ContentPerformance
from app.services.ai_service import ai_service


class MLService:
    """Service for ML-based content performance prediction"""
    
    def __init__(self):
        self.engagement_model = None
        self.reach_model = None
        self.scaler = StandardScaler()
        self.model_path = "models"
        self._ensure_model_directory()
    
    def _ensure_model_directory(self):
        """Ensure the model directory exists"""
        if not os.path.exists(self.model_path):
            os.makedirs(self.model_path)
    
    async def extract_features(self, content: str, platform: str) -> Dict[str, float]:
        """Extract features from content for ML prediction"""
        features = {
            # Content features
            'content_length': len(content),
            'word_count': len(content.split()),
            'hashtag_count': content.count('#'),
            'mention_count': content.count('@'),
            'url_count': content.count('http'),
            'emoji_count': sum(1 for c in content if ord(c) > 127),
            'question_marks': content.count('?'),
            'exclamation_marks': content.count('!'),
            
            # Platform features (one-hot encoding)
            'platform_instagram': 1 if platform == 'instagram' else 0,
            'platform_twitter': 1 if platform == 'twitter' else 0,
            'platform_linkedin': 1 if platform == 'linkedin' else 0,
            'platform_tiktok': 1 if platform == 'tiktok' else 0,
            
            # Time features
            'hour': datetime.now().hour,
            'day_of_week': datetime.now().weekday(),
            'is_weekend': 1 if datetime.now().weekday() >= 5 else 0,
            
            # Content type features (analyzed via AI)
            'sentiment_score': await self._analyze_sentiment(content),
            'readability_score': self._calculate_readability(content),
            'has_call_to_action': 1 if any(cta in content.lower() for cta in ['click', 'link', 'buy', 'shop', 'learn more']) else 0,
        }
        
        return features
    
    async def _analyze_sentiment(self, content: str) -> float:
        """Analyze sentiment of content (0-1 scale)"""
        # Simple sentiment analysis - in production, use a proper NLP model
        positive_words = ['amazing', 'awesome', 'excellent', 'fantastic', 'great', 'love', 'wonderful', 'best', 'happy', 'excited']
        negative_words = ['bad', 'terrible', 'awful', 'hate', 'worst', 'disappointed', 'sad', 'angry', 'frustrated']
        
        content_lower = content.lower()
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)
        
        total = positive_count + negative_count
        if total == 0:
            return 0.5  # Neutral
        
        return positive_count / total
    
    def _calculate_readability(self, content: str) -> float:
        """Calculate readability score (simplified)"""
        words = content.split()
        if not words:
            return 0.0
        
        avg_word_length = sum(len(word) for word in words) / len(words)
        sentences = content.count('.') + content.count('!') + content.count('?')
        
        if sentences == 0:
            sentences = 1
        
        avg_sentence_length = len(words) / sentences
        
        # Simple readability formula (lower is better)
        readability = (avg_word_length * 0.5 + avg_sentence_length * 0.5) / 10
        return min(1.0, readability)
    
    async def train_models(self, db: AsyncSession, user_id: int):
        """Train ML models on historical data"""
        # Get historical posts with analytics
        posts_query = select(Post, PostAnalytics).join(
            PostAnalytics, Post.id == PostAnalytics.post_id
        ).where(
            and_(
                Post.user_id == user_id,
                Post.published_at.isnot(None),
                Post.published_at >= datetime.utcnow() - timedelta(days=90)  # Last 90 days
            )
        )
        
        result = await db.execute(posts_query)
        data = result.all()
        
        if len(data) < 10:
            # Not enough data to train
            return False
        
        # Prepare training data
        X = []
        y_engagement = []
        y_reach = []
        
        for post, analytics in data:
            features = await self.extract_features(post.content, post.platform.value)
            X.append(list(features.values()))
            
            # Calculate engagement rate
            if analytics.reach > 0:
                engagement_rate = (analytics.likes + analytics.comments + analytics.shares) / analytics.reach
            else:
                engagement_rate = 0
            
            y_engagement.append(engagement_rate)
            y_reach.append(analytics.reach)
        
        X = np.array(X)
        y_engagement = np.array(y_engagement)
        y_reach = np.array(y_reach)
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train engagement model
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y_engagement, test_size=0.2, random_state=42
        )
        
        self.engagement_model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42
        )
        self.engagement_model.fit(X_train, y_train)
        
        # Train reach model
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y_reach, test_size=0.2, random_state=42
        )
        
        self.reach_model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42
        )
        self.reach_model.fit(X_train, y_train)
        
        # Save models
        self._save_models(user_id)
        
        return True
    
    def _save_models(self, user_id: int):
        """Save trained models to disk"""
        joblib.dump(self.engagement_model, f"{self.model_path}/engagement_model_{user_id}.pkl")
        joblib.dump(self.reach_model, f"{self.model_path}/reach_model_{user_id}.pkl")
        joblib.dump(self.scaler, f"{self.model_path}/scaler_{user_id}.pkl")
    
    def _load_models(self, user_id: int) -> bool:
        """Load trained models from disk"""
        try:
            self.engagement_model = joblib.load(f"{self.model_path}/engagement_model_{user_id}.pkl")
            self.reach_model = joblib.load(f"{self.model_path}/reach_model_{user_id}.pkl")
            self.scaler = joblib.load(f"{self.model_path}/scaler_{user_id}.pkl")
            return True
        except:
            return False
    
    async def predict_performance(
        self,
        content: str,
        platform: str,
        user_id: int
    ) -> Dict[str, float]:
        """Predict content performance"""
        # Try to load existing models
        if not self._load_models(user_id):
            # Use default predictions if no model exists
            return {
                'predicted_engagement_rate': 0.035,  # 3.5% default
                'predicted_reach': 1000,
                'confidence_score': 0.3  # Low confidence
            }
        
        # Extract features
        features = await self.extract_features(content, platform)
        X = np.array([list(features.values())])
        X_scaled = self.scaler.transform(X)
        
        # Make predictions
        engagement_pred = self.engagement_model.predict(X_scaled)[0]
        reach_pred = self.reach_model.predict(X_scaled)[0]
        
        # Calculate confidence based on feature importance
        confidence = self._calculate_confidence(features)
        
        return {
            'predicted_engagement_rate': float(engagement_pred),
            'predicted_reach': int(reach_pred),
            'confidence_score': confidence
        }
    
    def _calculate_confidence(self, features: Dict[str, float]) -> float:
        """Calculate prediction confidence"""
        # Simple confidence calculation based on feature completeness
        important_features = ['content_length', 'hashtag_count', 'sentiment_score']
        
        confidence = 0.5  # Base confidence
        
        # Adjust based on content quality indicators
        if features['content_length'] > 50:
            confidence += 0.1
        if features['hashtag_count'] > 0:
            confidence += 0.1
        if features['has_call_to_action']:
            confidence += 0.1
        if features['sentiment_score'] > 0.6:
            confidence += 0.1
        if features['readability_score'] < 0.7:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    async def analyze_content_elements(
        self,
        db: AsyncSession,
        user_id: int
    ) -> ContentPerformance:
        """Analyze what content elements perform best"""
        # Get recent high-performing posts
        posts_query = select(Post, PostAnalytics).join(
            PostAnalytics, Post.id == PostAnalytics.post_id
        ).where(
            and_(
                Post.user_id == user_id,
                Post.published_at >= datetime.utcnow() - timedelta(days=30)
            )
        ).order_by(PostAnalytics.engagement_rate.desc()).limit(20)
        
        result = await db.execute(posts_query)
        top_posts = result.all()
        
        if not top_posts:
            return None
        
        # Analyze patterns
        best_times = []
        best_days = []
        best_hashtags = []
        content_lengths = []
        
        for post, analytics in top_posts:
            if post.published_at:
                best_times.append(post.published_at.hour)
                best_days.append(post.published_at.strftime('%A'))
            
            # Extract hashtags
            hashtags = [word for word in post.content.split() if word.startswith('#')]
            best_hashtags.extend(hashtags)
            
            content_lengths.append(len(post.content))
        
        # Find most common patterns
        from collections import Counter
        
        time_counter = Counter(best_times)
        day_counter = Counter(best_days)
        hashtag_counter = Counter(best_hashtags)
        
        best_time = time_counter.most_common(1)[0][0] if time_counter else 9
        best_day = day_counter.most_common(1)[0][0] if day_counter else "Monday"
        top_hashtags = [tag for tag, _ in hashtag_counter.most_common(5)]
        avg_content_length = int(np.mean(content_lengths)) if content_lengths else 100
        
        # Create performance analysis
        performance = ContentPerformance(
            user_id=user_id,
            content_type="mixed",
            content_category="general",
            overall_score=75.0,  # Calculate based on actual data
            engagement_score=80.0,
            reach_score=70.0,
            conversion_score=75.0,
            best_time=f"{best_time}:00",
            best_day=best_day,
            best_hashtags=top_hashtags,
            best_content_length=avg_content_length
        )
        
        db.add(performance)
        await db.commit()
        await db.refresh(performance)
        
        return performance
    
    async def generate_optimization_suggestions(
        self,
        content: str,
        platform: str,
        predicted_performance: Dict[str, float]
    ) -> List[str]:
        """Generate suggestions to improve content performance"""
        suggestions = []
        
        features = await self.extract_features(content, platform)
        
        # Content length suggestions
        if features['content_length'] < 50:
            suggestions.append("📝 Add more detail - posts with 100-150 characters perform better")
        elif features['content_length'] > 280 and platform == 'twitter':
            suggestions.append("✂️ Consider shortening for Twitter's character limit")
        
        # Hashtag suggestions
        if features['hashtag_count'] == 0:
            suggestions.append("🏷️ Add 3-5 relevant hashtags to increase discoverability")
        elif features['hashtag_count'] > 10:
            suggestions.append("🏷️ Reduce hashtags - 5-7 hashtags typically perform best")
        
        # Engagement suggestions
        if features['question_marks'] == 0:
            suggestions.append("❓ Ask a question to encourage comments and engagement")
        
        if features['has_call_to_action'] == 0:
            suggestions.append("🎯 Add a clear call-to-action to guide your audience")
        
        # Sentiment suggestions
        if features['sentiment_score'] < 0.4:
            suggestions.append("😊 Consider a more positive tone - upbeat content gets more engagement")
        
        # Timing suggestions
        if features['hour'] < 8 or features['hour'] > 20:
            suggestions.append("⏰ Schedule for peak hours (9-10 AM or 7-9 PM) for better reach")
        
        # Platform-specific suggestions
        if platform == 'instagram' and features['emoji_count'] < 2:
            suggestions.append("✨ Add emojis - Instagram posts with emojis get 47% more engagement")
        
        if platform == 'linkedin' and features['emoji_count'] > 5:
            suggestions.append("👔 Reduce emojis for a more professional LinkedIn tone")
        
        return suggestions


# Singleton instance
ml_service = MLService()