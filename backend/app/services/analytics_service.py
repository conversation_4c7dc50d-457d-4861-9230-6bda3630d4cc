"""
Analytics service for data processing and insights generation
"""
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
import numpy as np
from collections import defaultdict

from app.models.user import User
from app.models.content import Post
from app.models.analytics import (
    PostAnalytics, ContentPerformance, UserAnalyticsSummary,
    CompetitorAnalysis, TrendAnalysis, ABTestResult
)
from app.schemas.analytics import (
    EngagementMetrics, ReachMetrics, AudienceMetrics,
    AnalyticsDashboardResponse, DashboardMetrics, PlatformBreakdown
)
from app.services.ai_service import ai_service


class AnalyticsService:
    """Service for analytics data processing and insights"""
    
    async def get_post_analytics(
        self,
        db: AsyncSession,
        post_id: int,
        platform: Optional[str] = None
    ) -> Optional[PostAnalytics]:
        """Get analytics for a specific post"""
        query = select(PostAnalytics).where(PostAnalytics.post_id == post_id)
        if platform:
            query = query.where(PostAnalytics.platform == platform)
        
        result = await db.execute(query.order_by(PostAnalytics.captured_at.desc()))
        return result.scalar_one_or_none()
    
    async def calculate_engagement_rate(
        self,
        analytics: PostAnalytics
    ) -> float:
        """Calculate engagement rate for a post"""
        if analytics.reach == 0:
            return 0.0
        
        total_engagement = (
            analytics.likes + analytics.comments + 
            analytics.shares + analytics.saves
        )
        return (total_engagement / analytics.reach) * 100
    
    async def get_user_summary(
        self,
        db: AsyncSession,
        user_id: int,
        period: str = "weekly"
    ) -> UserAnalyticsSummary:
        """Get or create user analytics summary"""
        # Calculate period dates
        end_date = datetime.utcnow()
        if period == "daily":
            start_date = end_date - timedelta(days=1)
        elif period == "weekly":
            start_date = end_date - timedelta(weeks=1)
        elif period == "monthly":
            start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(weeks=1)
        
        # Check if summary exists
        query = select(UserAnalyticsSummary).where(
            and_(
                UserAnalyticsSummary.user_id == user_id,
                UserAnalyticsSummary.period == period,
                UserAnalyticsSummary.period_start >= start_date
            )
        )
        result = await db.execute(query)
        summary = result.scalar_one_or_none()
        
        if not summary:
            # Create new summary
            summary = await self._create_user_summary(
                db, user_id, period, start_date, end_date
            )
        
        return summary
    
    async def _create_user_summary(
        self,
        db: AsyncSession,
        user_id: int,
        period: str,
        start_date: datetime,
        end_date: datetime
    ) -> UserAnalyticsSummary:
        """Create a new user analytics summary"""
        # Get all posts in period
        posts_query = select(Post).where(
            and_(
                Post.user_id == user_id,
                Post.created_at >= start_date,
                Post.created_at <= end_date
            )
        )
        posts_result = await db.execute(posts_query)
        posts = posts_result.scalars().all()
        
        # Aggregate analytics
        total_views = 0
        total_likes = 0
        total_comments = 0
        total_shares = 0
        total_clicks = 0
        top_posts = []
        
        for post in posts:
            # Get post analytics
            analytics_query = select(PostAnalytics).where(
                PostAnalytics.post_id == post.id
            ).order_by(PostAnalytics.captured_at.desc())
            analytics_result = await db.execute(analytics_query)
            analytics = analytics_result.scalar_one_or_none()
            
            if analytics:
                total_views += analytics.views
                total_likes += analytics.likes
                total_comments += analytics.comments
                total_shares += analytics.shares
                total_clicks += analytics.clicks
                
                # Track top posts
                engagement_rate = await self.calculate_engagement_rate(analytics)
                top_posts.append({
                    "post_id": post.id,
                    "engagement_rate": engagement_rate,
                    "views": analytics.views,
                    "likes": analytics.likes
                })
        
        # Sort top posts by engagement
        top_posts.sort(key=lambda x: x["engagement_rate"], reverse=True)
        top_posts = top_posts[:10]  # Keep top 10
        
        # Generate insights
        insights = await self._generate_insights(
            total_posts=len(posts),
            total_engagement=total_likes + total_comments + total_shares,
            total_views=total_views
        )
        
        # Create summary
        summary = UserAnalyticsSummary(
            user_id=user_id,
            period=period,
            period_start=start_date,
            period_end=end_date,
            total_posts=len(posts),
            total_views=total_views,
            total_likes=total_likes,
            total_comments=total_comments,
            total_shares=total_shares,
            total_clicks=total_clicks,
            avg_engagement_rate=self._calculate_avg_engagement_rate(
                total_likes + total_comments + total_shares,
                total_views
            ),
            top_posts=top_posts,
            insights=insights,
            recommendations=await self._generate_recommendations(insights)
        )
        
        db.add(summary)
        await db.commit()
        await db.refresh(summary)
        
        return summary
    
    def _calculate_avg_engagement_rate(
        self,
        total_engagement: int,
        total_views: int
    ) -> float:
        """Calculate average engagement rate"""
        if total_views == 0:
            return 0.0
        return (total_engagement / total_views) * 100
    
    async def _generate_insights(
        self,
        total_posts: int,
        total_engagement: int,
        total_views: int
    ) -> List[str]:
        """Generate AI-powered insights"""
        avg_engagement_per_post = total_engagement / max(total_posts, 1)
        avg_views_per_post = total_views / max(total_posts, 1)
        
        insights = []
        
        # Engagement insights
        if avg_engagement_per_post > 100:
            insights.append("🎯 Excellent engagement! Your content resonates well with your audience.")
        elif avg_engagement_per_post > 50:
            insights.append("👍 Good engagement levels. Consider experimenting with more interactive content.")
        else:
            insights.append("📈 Room for improvement in engagement. Try asking questions or creating polls.")
        
        # Views insights
        if avg_views_per_post > 1000:
            insights.append("👀 Strong reach! Your content is getting great visibility.")
        elif avg_views_per_post > 500:
            insights.append("📊 Decent reach. Consider using trending hashtags to increase visibility.")
        else:
            insights.append("🚀 Focus on increasing reach with better timing and hashtag strategy.")
        
        # Posting frequency
        if total_posts > 20:
            insights.append("📅 Great posting consistency! Keep maintaining this rhythm.")
        elif total_posts > 10:
            insights.append("⏰ Good posting frequency. Try to increase slightly for better results.")
        else:
            insights.append("📝 Consider posting more frequently to maintain audience engagement.")
        
        return insights
    
    async def _generate_recommendations(
        self,
        insights: List[str]
    ) -> List[str]:
        """Generate actionable recommendations based on insights"""
        recommendations = []
        
        # Base recommendations
        recommendations.append("🎯 Post during your audience's most active hours (typically 9-10 AM and 7-9 PM)")
        recommendations.append("🏷️ Use 5-10 relevant hashtags per post for optimal reach")
        recommendations.append("🖼️ Include high-quality visuals - posts with images get 2x more engagement")
        
        # Conditional recommendations based on insights
        if any("engagement" in insight.lower() and "room for improvement" in insight.lower() for insight in insights):
            recommendations.append("💬 Ask questions in your captions to encourage comments")
            recommendations.append("📊 Create polls and interactive content to boost engagement")
        
        if any("reach" in insight.lower() and "focus on" in insight.lower() for insight in insights):
            recommendations.append("🔍 Research and use trending hashtags in your niche")
            recommendations.append("🤝 Collaborate with other creators to expand your reach")
        
        return recommendations
    
    async def get_trending_content(
        self,
        db: AsyncSession,
        platform: str,
        limit: int = 10
    ) -> List[TrendAnalysis]:
        """Get trending content for a platform"""
        query = select(TrendAnalysis).where(
            TrendAnalysis.platform == platform
        ).order_by(
            TrendAnalysis.virality_score.desc(),
            TrendAnalysis.growth_rate.desc()
        ).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def analyze_competitors(
        self,
        db: AsyncSession,
        user_id: int,
        competitor_username: str,
        platform: str
    ) -> CompetitorAnalysis:
        """Analyze a competitor's performance"""
        # Check if recent analysis exists
        query = select(CompetitorAnalysis).where(
            and_(
                CompetitorAnalysis.user_id == user_id,
                CompetitorAnalysis.competitor_username == competitor_username,
                CompetitorAnalysis.platform == platform,
                CompetitorAnalysis.analyzed_at >= datetime.utcnow() - timedelta(days=7)
            )
        )
        result = await db.execute(query)
        existing = result.scalar_one_or_none()
        
        if existing:
            return existing
        
        # Create new analysis (mock data for now)
        analysis = CompetitorAnalysis(
            user_id=user_id,
            competitor_username=competitor_username,
            platform=platform,
            follower_count=15000,
            avg_engagement_rate=4.5,
            content_themes=["lifestyle", "fashion", "travel"],
            engagement_comparison=1.2,  # 20% higher
            strengths=["Consistent posting", "High-quality visuals", "Strong hashtag strategy"],
            opportunities=["Video content", "User-generated content", "Stories engagement"],
            recommendations=[
                "Increase posting frequency to match competitor",
                "Experiment with similar content themes",
                "Improve visual quality of posts"
            ]
        )
        
        db.add(analysis)
        await db.commit()
        await db.refresh(analysis)
        
        return analysis
    
    async def get_dashboard_data(
        self,
        db: AsyncSession,
        user_id: int
    ) -> AnalyticsDashboardResponse:
        """Get complete dashboard data for a user"""
        # Get user summary
        summary = await self.get_user_summary(db, user_id, "weekly")
        
        # Get platform breakdown (mock data for now)
        platforms = [
            PlatformBreakdown(
                platform="instagram",
                followers=5000,
                engagement_rate=3.5,
                top_posts=[
                    {"id": 1, "likes": 250, "comments": 45},
                    {"id": 2, "likes": 180, "comments": 32}
                ],
                best_posting_times=["9:00 AM", "7:00 PM"],
                audience_demographics={
                    "age": {"18-24": 35, "25-34": 45, "35-44": 20},
                    "gender": {"female": 60, "male": 40}
                }
            ),
            PlatformBreakdown(
                platform="twitter",
                followers=3000,
                engagement_rate=2.8,
                top_posts=[
                    {"id": 3, "likes": 120, "retweets": 45},
                    {"id": 4, "likes": 95, "retweets": 30}
                ],
                best_posting_times=["12:00 PM", "5:00 PM"],
                audience_demographics={
                    "interests": ["tech", "business", "marketing"],
                    "locations": {"US": 40, "UK": 20, "CA": 15, "Other": 25}
                }
            )
        ]
        
        # Get trending content
        trends = await self.get_trending_content(db, "instagram", 5)
        trend_data = [
            {
                "name": trend.trend_name,
                "category": trend.trend_category,
                "volume": trend.volume,
                "growth_rate": trend.growth_rate,
                "virality_score": trend.virality_score,
                "related_hashtags": trend.related_hashtags or []
            }
            for trend in trends
        ]
        
        # Create dashboard response
        dashboard = AnalyticsDashboardResponse(
            overview=DashboardMetrics(
                total_followers=8000,
                total_engagement=summary.total_likes + summary.total_comments + summary.total_shares,
                avg_engagement_rate=summary.avg_engagement_rate,
                total_reach=summary.total_views,
                growth_rate=5.2,
                top_performing_content=summary.top_posts[:5],
                content_calendar=[],  # Would be populated from scheduled posts
                recent_alerts=[
                    {"type": "milestone", "message": "You reached 8K followers! 🎉"},
                    {"type": "trending", "message": "#TechTrends is trending in your niche"}
                ]
            ),
            platforms=platforms,
            trends=trend_data,
            competitors=[],  # Would be populated from competitor analysis
            recommendations=[
                {
                    "type": "content",
                    "priority": "high",
                    "action": "Create more video content - it's getting 3x more engagement"
                },
                {
                    "type": "timing",
                    "priority": "medium",
                    "action": "Post at 9 AM on weekdays for maximum reach"
                },
                {
                    "type": "hashtags",
                    "priority": "medium",
                    "action": "Use trending hashtags: #TechTrends, #Innovation2024"
                }
            ]
        )
        
        return dashboard


# Singleton instance
analytics_service = AnalyticsService()