from typing import Dict, Any, List, Optional
from openai import OpenAI
from datetime import datetime
import json
import time

from app.core.config import settings
from app.core.websocket import ProgressTracker


class PerplexityService:
    def __init__(self, api_key: Optional[str] = None):
        self.client = None
        self.initialization_error = None
        
        api_key_to_use = api_key or settings.PERPLEXITY_API_KEY
        if not api_key_to_use:
            self.initialization_error = "No Perplexity API key provided"
            print(f"Warning: {self.initialization_error}")
            return
            
        try:
            self.client = OpenAI(
                api_key=api_key_to_use,
                base_url="https://api.perplexity.ai"
            )
            print("✅ Perplexity client initialized successfully")
        except Exception as e:
            self.initialization_error = f"Failed to initialize Perplexity client: {e}"
            print(f"❌ {self.initialization_error}")
            self.client = None
    
    async def research_topic(
        self,
        topic: str,
        additional_context: Optional[str] = None,
        deep_research: bool = False,
        progress_tracker: Optional[ProgressTracker] = None
    ) -> Dict[str, Any]:
        """Research a topic using Perplexity API with progressive enhancement"""
        
        if not self.client:
            raise Exception(f"Perplexity client not available: {self.initialization_error}")
        
        # Build research query
        query = self._build_research_query(topic, additional_context)
        
        print(f"🔍 Starting {'deep' if deep_research else 'standard'} research for '{topic}'")
        start_time = time.time()
        
        # Update progress if tracker provided
        if progress_tracker:
            await progress_tracker.update(1, f"Starting {'deep' if deep_research else 'standard'} research for '{topic}'")
        
        # Progressive research approach
        if deep_research:
            return await self._deep_research_workflow(topic, query, additional_context, progress_tracker)
        else:
            return await self._standard_research(query, topic, progress_tracker)
    
    async def _standard_research(self, query: str, topic: str, progress_tracker: Optional[ProgressTracker] = None) -> Dict[str, Any]:
        """Standard research with fallback mechanisms"""
        models_to_try = [settings.PERPLEXITY_MODEL, "sonar", "sonar-pro"]
        start_time = time.time()
        
        for model in models_to_try:
            try:
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[
                        {
                            "role": "system",
                            "content": """You are an expert researcher specializing in social media content. Focus on:

FACTS & DATA:
- Latest statistics and numbers (with dates)
- Key metrics and percentages
- Recent research findings

TRENDS & INSIGHTS:
- Current developments (last 6 months)
- Industry perspectives
- Notable changes or shifts

Keep responses concise, factual, and source-backed. Prioritize recent, actionable information over general knowledge."""
                        },
                        {
                            "role": "user",
                            "content": query
                        }
                    ],
                    temperature=0.1,
                    max_tokens=1500,
                    timeout=20
                )
                
                content = response.choices[0].message.content
                
                # Get search results if available
                search_results = self._extract_search_results(response)
                
                # Parse the research response
                research_data = self._parse_research_response(content, topic, search_results)
                
                # Log success
                end_time = time.time()
                duration = round(end_time - start_time, 2)
                findings_count = len(research_data.get('findings', []))
                sources_count = len(research_data.get('sources', []))
                
                print(f"✅ Standard research completed for '{topic}' in {duration}s using {model}")
                print(f"   📊 {findings_count} findings, {sources_count} sources")
                
                return research_data
                
            except Exception as e:
                print(f"❌ Failed with model {model}: {str(e)}")
                if model == models_to_try[-1]:  # Last model, re-raise
                    error_msg = str(e)
                    if "timeout" in error_msg.lower():
                        raise Exception(f"Research request timed out for '{topic}'. Please try again or use standard research.")
                    elif "rate_limit" in error_msg.lower() or "429" in error_msg:
                        raise Exception(f"Rate limit exceeded. Please wait a moment before trying again.")
                    elif "authentication" in error_msg.lower() or "401" in error_msg:
                        raise Exception(f"Perplexity API authentication failed. Please check your API key.")
                    else:
                        raise Exception(f"Research failed for '{topic}': {error_msg}")
                continue  # Try next model
    
    async def _deep_research_workflow(self, topic: str, query: str, additional_context: Optional[str], progress_tracker: Optional[ProgressTracker] = None) -> Dict[str, Any]:
        """Deep research workflow using sonar-deep-research model"""
        print(f"🔬 Starting deep research workflow for '{topic}'")
        start_time = time.time()
        
        # Use the dedicated deep research model with extended timeout
        if progress_tracker:
            await progress_tracker.update(2, "🔍 Initiating comprehensive deep research analysis...")
        
        try:
            # Build comprehensive research prompt
            deep_research_query = f"""Conduct comprehensive research on: {topic}

Please provide:
1. Current state and overview (2024-2025 data)
2. Latest statistics, metrics, and quantitative data
3. Recent trends and developments
4. Market analysis and competitive landscape
5. Expert insights and future outlook
6. Key challenges and opportunities
7. Actionable insights for social media content

Focus on accuracy, recency, and specific data points with sources."""
            
            if additional_context:
                deep_research_query += f"\n\nAdditional context: {additional_context}"
            
            print("🔬 Executing deep research with sonar-deep-research model...")
            
            response = self.client.chat.completions.create(
                model=settings.PERPLEXITY_DEEP_RESEARCH_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": """You are an expert researcher conducting comprehensive analysis. 
Provide detailed, factual information with specific data points, statistics, and credible sources.
Structure your response with clear sections and actionable insights."""
                    },
                    {
                        "role": "user",
                        "content": deep_research_query
                    }
                ],
                temperature=0.1,
                max_tokens=4000,  # Increased for comprehensive reports
                timeout=300,  # 5 minutes timeout for deep research
                search_recency_filter="month",
                return_citations=True
            )
            
            content = response.choices[0].message.content
            search_results = self._extract_search_results(response)
            
            # Parse the comprehensive research response
            research_data = self._parse_deep_research_response(content, topic, search_results)
            
            end_time = time.time()
            duration = round(end_time - start_time, 2)
            
            print(f"✅ Deep research completed for '{topic}' in {duration}s")
            print(f"   📊 {len(research_data.get('findings', []))} key findings")
            print(f"   📚 {len(research_data.get('sources', []))} sources")
            
            if progress_tracker:
                await progress_tracker.complete(
                    f"✅ Deep research completed! Found {len(research_data.get('findings', []))} key insights",
                    {"duration": duration, "findings_count": len(research_data.get('findings', []))}
                )
            
            return research_data
            
        except Exception as e:
            print(f"❌ Deep research failed: {str(e)}")
            if progress_tracker:
                await progress_tracker.update(3, "⚠️ Deep research failed, falling back to standard research...")
            
            # Fallback to standard research
            return await self._standard_research(query, topic, progress_tracker)
    
    async def _research_phase(self, query: str, model: str, timeout: int = 20) -> Dict[str, Any]:
        """Execute a single research phase"""
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "system",
                        "content": """You are an expert researcher. Provide comprehensive, factual information with:
                        
1. SPECIFIC DATA: Numbers, percentages, dates, statistics
2. CREDIBLE SOURCES: Recent studies, reports, expert opinions
3. CURRENT TRENDS: Latest developments and changes
4. ACTIONABLE INSIGHTS: Practical implications and opportunities

Focus on accuracy and recency. Cite sources when possible."""
                    },
                    {
                        "role": "user",
                        "content": query
                    }
                ],
                temperature=0.1,
                max_tokens=2000,
                timeout=timeout
            )
            
            content = response.choices[0].message.content
            search_results = self._extract_search_results(response)
            
            return {
                "content": content,
                "search_results": search_results,
                "query": query
            }
            
        except Exception as e:
            print(f"⚠️ Research phase failed: {str(e)}")
            return {"content": "", "search_results": [], "query": query}
    
    def _extract_search_results(self, response) -> List[Dict[str, Any]]:
        """Extract search results from Perplexity response"""
        search_results = []
        
        if hasattr(response, 'search_results'):
            search_results = response.search_results
        elif hasattr(response, 'choices') and len(response.choices) > 0:
            choice = response.choices[0]
            if hasattr(choice, 'search_results'):
                search_results = choice.search_results
        
        return search_results or []
    
    def _combine_research_phases(self, topic: str, *phases) -> Dict[str, Any]:
        """Combine multiple research phases into comprehensive results"""
        all_findings = []
        all_sources = []
        combined_content = []
        all_search_results = []
        
        phase_names = ["Broad Research", "Statistical Data", "Trend Analysis", "Market Analysis"]
        
        for i, phase in enumerate(phases):
            if not phase or not phase.get("content"):
                continue
                
            phase_name = phase_names[i] if i < len(phase_names) else f"Phase {i+1}"
            
            # Add phase header to content
            combined_content.append(f"\n=== {phase_name} ===\n")
            combined_content.append(phase["content"])
            
            # Extract findings from this phase
            phase_findings = self._extract_findings_from_content(phase["content"])
            all_findings.extend(phase_findings)
            
            # Extract sources from this phase
            phase_sources = self._extract_sources_from_content(phase["content"])
            all_sources.extend(phase_sources)
            
            # Add search results
            if phase.get("search_results"):
                all_search_results.extend(phase["search_results"])
        
        # Remove duplicates and limit results
        unique_findings = list(dict.fromkeys(all_findings))[:10]  # Top 10 unique findings
        unique_sources = list(dict.fromkeys(all_sources))[:8]     # Top 8 unique sources
        
        full_content = "\n".join(combined_content)
        
        return {
            "query": topic,
            "findings": unique_findings,
            "sources": unique_sources,
            "full_content": full_content,
            "search_results": all_search_results[:10],  # Top 10 search results
            "timestamp": datetime.utcnow().isoformat(),
            "research_type": "deep_research",
            "phases_completed": len([p for p in phases if p and p.get("content")])
        }
    
    def _extract_findings_from_content(self, content: str) -> List[str]:
        """Extract key findings from content"""
        findings = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or len(line) < 20:
                continue
                
            # Look for data-rich sentences
            if any(keyword in line.lower() for keyword in [
                'according to', 'study shows', 'research indicates', 'data reveals',
                'statistics show', 'survey found', 'report states', 'analysis shows',
                '%', 'percent', 'million', 'billion', 'trillion', 'thousand',
                'increased by', 'decreased by', 'grew by', 'fell by', 'rose to',
                '2024', '2025', 'recent', 'latest', 'current', 'new study'
            ]):
                if len(line) < 300:  # Reasonable length
                    findings.append(line)
        
        return findings[:5]  # Top 5 findings per phase
    
    def _extract_sources_from_content(self, content: str) -> List[str]:
        """Extract sources from content"""
        sources = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in [
                'source:', 'according to', 'study by', 'research from',
                'report by', 'published by', 'conducted by'
            ]):
                if len(line) < 200:  # Reasonable length
                    sources.append(line)
        
        return sources[:3]  # Top 3 sources per phase
    
    def _build_research_query(
        self,
        topic: str,
        additional_context: Optional[str] = None
    ) -> str:
        """Build a comprehensive research query"""
        
        query_parts = [
            f"Research: {topic}",
            "",
            "Focus on:",
            "• Recent statistics (2024-2025) with specific numbers",
            "• Latest developments and news",
            "• Key trends and market insights",
            "• Expert quotes or industry perspectives",
            "",
            "Prioritize: Current data, specific metrics, actionable insights for social media content."
        ]
        
        if additional_context:
            query_parts.extend([
                "",
                f"Additional context: {additional_context}"
            ])
        
        return "\n".join(query_parts)
    
    def _parse_research_response(
        self,
        content: str,
        topic: str,
        search_results: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Parse the research response into structured data"""
        
        # Extract key findings (simplified parsing)
        lines = content.split('\n')
        findings = []
        sources = []
        
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Look for statistical data, facts, or insights (improved keywords)
            if any(keyword in line.lower() for keyword in [
                'according to', 'study shows', 'research indicates', 'data reveals', 
                'statistics show', 'recent survey', 'report found', 'analysis shows',
                '%', 'percent', 'million', 'billion', 'trillion', 'thousand',
                'increase', 'decrease', 'growth', 'decline', 'rose by', 'fell by',
                '2024', '2025', 'this year', 'last year', 'quarterly', 'annually'
            ]):
                if len(line) > 15 and len(line) < 300:  # Filter reasonable length
                    findings.append(line)
            
            # Look for source indicators
            if any(keyword in line.lower() for keyword in [
                'source:', 'according to', 'study by', 'research from'
            ]):
                sources.append(line)
        
        # If search_results are available, extract additional sources
        if search_results:
            for result in search_results[:3]:  # Limit to top 3 search results
                if 'title' in result:
                    sources.append(result['title'])
                elif 'url' in result:
                    sources.append(result['url'])
        
        # Limit findings and sources
        findings = findings[:5]
        sources = sources[:5]  # Increased from 3 to 5 to include search results
        
        # If no specific findings found, extract key sentences
        if not findings:
            sentences = content.split('.')
            findings = [
                sentence.strip() + '.'
                for sentence in sentences
                if len(sentence.strip()) > 20 and len(sentence.strip()) < 200
            ][:5]
        
        result = {
            "query": topic,
            "findings": findings,
            "sources": sources,
            "full_content": content,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Add search results if available
        if search_results:
            result["search_results"] = search_results
            
        return result
    
    def _parse_deep_research_response(
        self,
        content: str,
        topic: str,
        search_results: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Parse the deep research response into structured data"""
        
        # Extract comprehensive findings from deep research
        lines = content.split('\n')
        findings = []
        sources = []
        sections = {}
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Detect section headers (numbered or with keywords)
            if any(pattern in line.lower() for pattern in [
                '1.', '2.', '3.', '4.', '5.', '6.', '7.',
                'current state', 'statistics', 'trends', 'market analysis',
                'expert insights', 'challenges', 'actionable insights'
            ]):
                current_section = line
                sections[current_section] = []
                continue
            
            # Add content to current section
            if current_section and line:
                sections[current_section].append(line)
            
            # Extract key findings with data
            if any(keyword in line.lower() for keyword in [
                'according to', 'study shows', 'research indicates', 'data reveals',
                'statistics show', 'survey found', 'report states', 'analysis shows',
                '%', 'percent', 'million', 'billion', 'trillion', 'thousand',
                'increased by', 'decreased by', 'grew by', 'fell by', 'rose to',
                '2024', '2025', 'recent', 'latest', 'current', 'new study'
            ]):
                if len(line) > 20 and len(line) < 400:
                    findings.append(line)
            
            # Extract sources
            if any(keyword in line.lower() for keyword in [
                'source:', 'according to', 'study by', 'research from',
                'report by', 'published by', 'conducted by', 'data from'
            ]):
                if len(line) < 250:
                    sources.append(line)
        
        # Process search results if available
        if search_results:
            for result in search_results[:5]:
                if 'title' in result and 'url' in result:
                    sources.append(f"{result['title']} - {result['url']}")
                elif 'title' in result:
                    sources.append(result['title'])
        
        # Limit and deduplicate
        findings = list(dict.fromkeys(findings))[:15]  # More findings for deep research
        sources = list(dict.fromkeys(sources))[:10]    # More sources for deep research
        
        # If no findings, extract from sections
        if not findings and sections:
            for section, content_lines in sections.items():
                for line in content_lines[:2]:  # First 2 lines from each section
                    if len(line) > 20:
                        findings.append(line)
        
        result = {
            "query": topic,
            "findings": findings,
            "sources": sources,
            "full_content": content,
            "sections": sections,  # Include structured sections
            "timestamp": datetime.utcnow().isoformat(),
            "research_type": "deep_research"
        }
        
        # Add search results if available
        if search_results:
            result["search_results"] = search_results
            
        return result
    
    async def get_trending_topics(
        self,
        category: Optional[str] = None
    ) -> List[str]:
        """Get trending topics for content inspiration"""
        
        query = "What are the current trending topics"
        if category:
            query += f" in {category}"
        query += " that would be good for social media content?"
        
        try:
            response = self.client.chat.completions.create(
                model=settings.PERPLEXITY_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a trend analyst. Provide a list of current trending topics that are suitable for social media content creation."
                    },
                    {
                        "role": "user",
                        "content": query
                    }
                ],
                temperature=0.2,
                max_tokens=800,
                timeout=15
            )
            
            content = response.choices[0].message.content
            
            # Extract topics from the response
            topics = self._extract_topics(content)
            
            return topics
            
        except Exception as e:
            raise Exception(f"Perplexity API error: {str(e)}")
    
    def _extract_topics(self, content: str) -> List[str]:
        """Extract topic list from response"""
        
        lines = content.split('\n')
        topics = []
        
        for line in lines:
            line = line.strip()
            # Look for numbered or bulleted lists
            if any(line.startswith(prefix) for prefix in ['1.', '2.', '3.', '4.', '5.', '-', '•', '*']):
                # Clean up the topic
                topic = line
                for prefix in ['1.', '2.', '3.', '4.', '5.', '-', '•', '*']:
                    topic = topic.replace(prefix, '').strip()
                
                if len(topic) > 10:  # Only meaningful topics
                    topics.append(topic)
        
        return topics[:10]  # Limit to top 10



# Singleton instance
perplexity_service = PerplexityService()