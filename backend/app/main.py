from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.core.config import settings
from app.api.routes import auth, content, schedule, platforms, websocket
# from app.api.routes import auth_v2 as auth  # Temporarily use original auth
# from app.api.routes import analytics  # Will enable after updating Post model
# from app.api.routes import ml_predictions  # ML predictions API
from app.models.database import init_db
# from app.core.redis_client import redis_client  # Temporarily disabled
# from app.middleware.rate_limit import RateLimitMiddleware  # Temporarily disabled


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await init_db()
    # await redis_client.connect()  # Temporarily disabled
    # print("✅ Redis connected")
    print("✅ Database initialized")
    yield
    # Shutdown
    # await redis_client.disconnect()  # Temporarily disabled
    # print("❌ Redis disconnected")
    print("❌ Shutting down")


app = FastAPI(
    title="SocialAI Pro API",
    description="AI-powered social media content automation platform",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiting - temporarily disabled
# app.add_middleware(
#     RateLimitMiddleware,
#     calls=settings.RATE_LIMIT_PER_MINUTE,
#     period=60
# )

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(content.router, prefix="/api/content", tags=["content"])
app.include_router(schedule.router, prefix="/api/schedule", tags=["schedule"])
app.include_router(platforms.router, prefix="/api/platforms", tags=["platforms"])
# app.include_router(analytics.router, prefix="/api/analytics", tags=["analytics"])  # Will enable after updating Post model
# app.include_router(ml_predictions.router, prefix="/api/ml", tags=["ml"])  # ML predictions
app.include_router(websocket.router, prefix="/api/ws", tags=["websocket"])


@app.get("/")
async def root():
    return {"message": "Welcome to SocialAI Pro API"}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}