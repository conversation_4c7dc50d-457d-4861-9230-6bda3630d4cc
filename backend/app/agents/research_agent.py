"""
Research Agent - Gathers and analyzes information for content creation
"""
from typing import Dict, Any, List, Optional
import asyncio
from datetime import datetime

from app.agents.base import BaseAgent, AgentContext
from app.services.perplexity_service import perplexity_service
from app.core.cache import cache_result
from app.middleware.rate_limit import perplexity_rate_limiter


class ResearchAgent(BaseAgent):
    """Agent responsible for researching topics and gathering data"""
    
    def __init__(self):
        super().__init__(
            name="ResearchAgent",
            description="Gathers relevant information and insights for content creation"
        )
        self.perplexity = perplexity_service
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """Research the given topic"""
        # Check rate limit
        if not await perplexity_rate_limiter.check_and_update(context.user_id):
            return {
                "status": "rate_limited",
                "error": "Research API rate limit exceeded"
            }
        
        try:
            # Perform main research
            research_data = await self._research_topic(
                context.topic,
                context.additional_context,
                context.platform
            )
            
            # Get trending angles if no specific research data
            if not research_data.get("findings"):
                trending_data = await self._get_trending_angles(context.topic)
                research_data["trending_angles"] = trending_data
            
            # Analyze competitors if requested
            if context.constraints and context.constraints.get("analyze_competitors"):
                competitor_data = await self._analyze_competitors(
                    context.topic,
                    context.platform
                )
                research_data["competitor_insights"] = competitor_data
            
            # Get platform-specific insights
            platform_insights = await self._get_platform_insights(
                context.topic,
                context.platform
            )
            research_data["platform_insights"] = platform_insights
            
            return {
                "status": "success",
                "output": research_data,
                "metadata": {
                    "sources_count": len(research_data.get("sources", [])),
                    "findings_count": len(research_data.get("findings", [])),
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    @cache_result(expire=1800, prefix="research")
    async def _research_topic(
        self,
        topic: str,
        additional_context: Optional[str],
        platform: str
    ) -> Dict[str, Any]:
        """Core research functionality with caching"""
        # Build enhanced query based on platform
        enhanced_query = self._build_enhanced_query(topic, platform)
        
        # Perform research
        research_data = await self.perplexity.research_topic(
            enhanced_query,
            additional_context,
            deep_research=True
        )
        
        # Extract key insights
        insights = self._extract_key_insights(research_data)
        research_data["key_insights"] = insights
        
        return research_data
    
    def _build_enhanced_query(self, topic: str, platform: str) -> str:
        """Build platform-optimized research query"""
        platform_queries = {
            "twitter": f"{topic} viral tweets trending discussions twitter threads",
            "linkedin": f"{topic} professional insights industry trends thought leadership",
            "instagram": f"{topic} visual content trends hashtags engagement",
            "tiktok": f"{topic} viral videos trends challenges youth culture"
        }
        
        base_query = platform_queries.get(platform.lower(), topic)
        return f"{base_query} 2024 2025 latest statistics data"
    
    def _extract_key_insights(self, research_data: Dict[str, Any]) -> List[str]:
        """Extract the most important insights from research"""
        insights = []
        
        # Extract insights from findings
        findings = research_data.get("findings", [])
        for finding in findings[:3]:  # Top 3 findings
            # Look for statistical data
            if any(char.isdigit() for char in finding):
                insights.append(finding)
        
        # Extract insights from full content
        content = research_data.get("full_content", "")
        if content:
            # Simple extraction of sentences with numbers/statistics
            sentences = content.split('.')
            for sentence in sentences:
                if any(word in sentence.lower() for word in ['percent', '%', 'million', 'billion']):
                    insights.append(sentence.strip() + '.')
                    if len(insights) >= 5:
                        break
        
        return insights[:5]  # Return top 5 insights
    
    async def _get_trending_angles(self, topic: str) -> List[str]:
        """Get trending angles for the topic"""
        try:
            angles = await self.perplexity.get_trending_topics(topic)
            return angles[:5]
        except:
            return []
    
    async def _analyze_competitors(
        self,
        topic: str,
        platform: str
    ) -> Dict[str, Any]:
        """Analyze competitor content on the topic"""
        query = f"top performing {platform} posts about {topic} engagement strategies"
        
        try:
            competitor_data = await self.perplexity.research_topic(query)
            
            return {
                "top_strategies": self._extract_strategies(competitor_data),
                "content_gaps": self._identify_content_gaps(competitor_data),
                "engagement_tactics": self._extract_engagement_tactics(competitor_data)
            }
        except:
            return {}
    
    def _extract_strategies(self, data: Dict[str, Any]) -> List[str]:
        """Extract successful strategies from competitor analysis"""
        strategies = []
        findings = data.get("findings", [])
        
        strategy_keywords = ["strategy", "approach", "method", "technique", "tactic"]
        
        for finding in findings:
            if any(keyword in finding.lower() for keyword in strategy_keywords):
                strategies.append(finding)
        
        return strategies[:3]
    
    def _identify_content_gaps(self, data: Dict[str, Any]) -> List[str]:
        """Identify content gaps in the market"""
        # This is a simplified implementation
        gaps = [
            "Detailed case studies with ROI metrics",
            "Behind-the-scenes content and processes",
            "Interactive content and community engagement",
            "Educational series and tutorials"
        ]
        return gaps[:2]
    
    def _extract_engagement_tactics(self, data: Dict[str, Any]) -> List[str]:
        """Extract engagement tactics from research"""
        tactics = []
        content = data.get("full_content", "")
        
        engagement_keywords = ["engage", "interaction", "response", "comment", "share", "viral"]
        
        sentences = content.split('.')
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in engagement_keywords):
                tactics.append(sentence.strip())
                if len(tactics) >= 3:
                    break
        
        return tactics
    
    async def _get_platform_insights(
        self,
        topic: str,
        platform: str
    ) -> Dict[str, Any]:
        """Get platform-specific insights"""
        platform_insights = {
            "twitter": {
                "optimal_length": "280 characters with thread potential",
                "best_practices": [
                    "Use 1-2 relevant hashtags",
                    "Include statistics or data points",
                    "Ask questions to drive engagement",
                    "Use thread format for complex topics"
                ],
                "peak_times": "9-10 AM and 7-9 PM EST"
            },
            "linkedin": {
                "optimal_length": "1300-2000 characters",
                "best_practices": [
                    "Start with a personal story or insight",
                    "Use line breaks for readability",
                    "Include 3-5 relevant hashtags",
                    "End with a question or call-to-action"
                ],
                "peak_times": "7-8 AM and 5-6 PM on weekdays"
            }
        }
        
        return platform_insights.get(platform.lower(), {
            "optimal_length": "Varies by platform",
            "best_practices": ["Research platform-specific best practices"],
            "peak_times": "Varies by audience"
        })