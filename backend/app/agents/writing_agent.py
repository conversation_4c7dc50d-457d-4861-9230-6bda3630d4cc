"""
Writing Agent - Creates platform-optimized content based on research
"""
from typing import Dict, Any, List, Optional
import re
from datetime import datetime

from app.agents.base import BaseAgent, AgentContext
from app.services.ai_service import ai_service, AIProvider
from app.core.cache import cache_result
from app.middleware.rate_limit import claude_rate_limiter, openai_rate_limiter


class WritingAgent(BaseAgent):
    """Agent responsible for creating engaging content"""
    
    def __init__(self):
        super().__init__(
            name="WritingAgent",
            description="Creates compelling, platform-optimized content"
        )
        self.ai_service = ai_service
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """Generate content based on research and context"""
        # Get research data from context history
        research_data = self._extract_research_data(context)
        
        # Determine AI provider based on user preferences
        provider = self._select_ai_provider(context)
        
        # Check rate limit
        if not await self._check_rate_limit(provider, context.user_id):
            return {
                "status": "rate_limited",
                "error": f"{provider} API rate limit exceeded"
            }
        
        try:
            # Generate multiple content variations
            content_variations = await self._generate_content(
                context.topic,
                context.platform,
                provider,
                research_data,
                context.additional_context
            )
            
            # Enhance content with engagement elements
            enhanced_content = []
            for variation in content_variations:
                enhanced = await self._enhance_content(
                    variation,
                    context.platform,
                    research_data
                )
                enhanced_content.append(enhanced)
            
            # Score and rank variations
            scored_content = self._score_content(enhanced_content, context.platform)
            
            return {
                "status": "success",
                "output": {
                    "variations": scored_content,
                    "best_variation": scored_content[0],
                    "writing_tips": self._get_writing_tips(context.platform)
                },
                "metadata": {
                    "provider": provider,
                    "variations_count": len(scored_content),
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _extract_research_data(self, context: AgentContext) -> Optional[Dict[str, Any]]:
        """Extract research data from context history"""
        for message in reversed(context.history):
            if message.sender.startswith("ResearchAgent") and message.message_type == "result":
                return message.content
        
        return context.research_data
    
    def _select_ai_provider(self, context: AgentContext) -> AIProvider:
        """Select the best AI provider based on context"""
        if context.constraints and "ai_provider" in context.constraints:
            return AIProvider(context.constraints["ai_provider"])
        
        # Default selection based on platform
        platform_providers = {
            "twitter": AIProvider.XAI,  # Grok for X/Twitter
            "linkedin": AIProvider.CLAUDE,  # Claude for professional content
            "instagram": AIProvider.OPENAI,  # GPT for creative captions
            "tiktok": AIProvider.GEMINI  # Gemini for trendy content
        }
        
        return platform_providers.get(context.platform.lower(), AIProvider.CLAUDE)
    
    async def _check_rate_limit(self, provider: AIProvider, user_id: int) -> bool:
        """Check rate limit for the selected provider"""
        rate_limiters = {
            AIProvider.CLAUDE: claude_rate_limiter,
            AIProvider.OPENAI: openai_rate_limiter,
            AIProvider.GEMINI: None,  # Gemini has generous limits
            AIProvider.XAI: None  # XAI limits unknown
        }
        
        limiter = rate_limiters.get(provider)
        if limiter:
            return await limiter.check_and_update(user_id)
        return True
    
    async def _generate_content(
        self,
        topic: str,
        platform: str,
        provider: AIProvider,
        research_data: Optional[Dict[str, Any]],
        additional_context: Optional[str]
    ) -> List[Dict[str, Any]]:
        """Generate content variations"""
        # Use AI service to generate content
        variations = await self.ai_service.generate_content(
            topic,
            platform,
            provider,
            research_data,
            additional_context
        )
        
        return variations
    
    async def _enhance_content(
        self,
        content: Dict[str, Any],
        platform: str,
        research_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Enhance content with platform-specific elements"""
        enhanced = content.copy()
        
        # Add hooks based on platform
        if platform.lower() == "twitter":
            enhanced["hook"] = self._extract_hook(content["content"], 50)
            enhanced["thread_potential"] = self._assess_thread_potential(content["content"])
            
        elif platform.lower() == "linkedin":
            enhanced["headline"] = self._create_headline(content["content"])
            enhanced["cta"] = self._generate_cta(content["content"])
            
        # Add data points from research
        if research_data and research_data.get("key_insights"):
            enhanced["supporting_data"] = research_data["key_insights"][:2]
        
        # Add engagement predictions
        enhanced["predicted_engagement"] = self._predict_engagement(
            content["content"],
            platform
        )
        
        return enhanced
    
    def _extract_hook(self, content: str, max_length: int) -> str:
        """Extract or create a compelling hook"""
        # Get first sentence or phrase
        sentences = re.split(r'[.!?]', content)
        if sentences:
            hook = sentences[0].strip()
            if len(hook) > max_length:
                hook = hook[:max_length-3] + "..."
            return hook
        return content[:max_length]
    
    def _assess_thread_potential(self, content: str) -> bool:
        """Assess if content would work well as a thread"""
        # Simple heuristic: long content or multiple points
        return len(content) > 500 or content.count('\n') > 3
    
    def _create_headline(self, content: str) -> str:
        """Create a LinkedIn-style headline"""
        # Extract key message
        first_line = content.split('\n')[0]
        
        # Add emoji if appropriate
        if any(word in first_line.lower() for word in ['success', 'growth', 'achieve']):
            return f"🚀 {first_line}"
        elif any(word in first_line.lower() for word in ['learn', 'lesson', 'insight']):
            return f"💡 {first_line}"
        
        return first_line
    
    def _generate_cta(self, content: str) -> str:
        """Generate a call-to-action"""
        ctas = [
            "What's your experience with this?",
            "Share your thoughts below 👇",
            "Have you faced similar challenges?",
            "What would you add to this list?",
            "Tag someone who needs to see this!"
        ]
        
        # Select CTA based on content type
        if "tips" in content.lower() or "steps" in content.lower():
            return ctas[3]
        elif "story" in content.lower() or "experience" in content.lower():
            return ctas[0]
        else:
            return ctas[1]
    
    def _predict_engagement(self, content: str, platform: str) -> Dict[str, float]:
        """Predict engagement metrics (simplified)"""
        # Basic scoring based on content characteristics
        score = 50  # Base score
        
        # Platform-specific scoring
        if platform.lower() == "twitter":
            if len(content) < 100:
                score += 10  # Concise content
            if "?" in content:
                score += 15  # Questions drive engagement
            if any(word in content.lower() for word in ['breaking', 'just in', 'thread']):
                score += 20
                
        elif platform.lower() == "linkedin":
            if len(content) > 500:
                score += 15  # Longer form content
            if content.count('\n') > 5:
                score += 10  # Well-formatted
            if any(word in content.lower() for word in ['lesson', 'learned', 'insight']):
                score += 20
        
        # Normalize score
        score = min(100, max(0, score))
        
        return {
            "overall_score": score / 100,
            "shareability": (score + 10) / 100,
            "comment_potential": (score - 10) / 100
        }
    
    def _score_content(
        self,
        content_list: List[Dict[str, Any]],
        platform: str
    ) -> List[Dict[str, Any]]:
        """Score and rank content variations"""
        scored_content = []
        
        for content in content_list:
            score = 0
            
            # Length optimization
            optimal_lengths = {
                "twitter": 280,
                "linkedin": 1300,
                "instagram": 150,
                "tiktok": 100
            }
            
            optimal = optimal_lengths.get(platform.lower(), 500)
            length_diff = abs(len(content["content"]) - optimal)
            length_score = max(0, 100 - (length_diff / optimal * 100))
            score += length_score * 0.3
            
            # Engagement prediction score
            engagement_score = content.get("predicted_engagement", {}).get("overall_score", 0.5)
            score += engagement_score * 100 * 0.4
            
            # Readability and structure
            if content["content"].count('\n') > 2:
                score += 10  # Good structure
            if '?' in content["content"]:
                score += 10  # Questions
            if any(emoji in content["content"] for emoji in ['🚀', '💡', '🎯', '✨']):
                score += 5  # Emojis
            
            content["score"] = score
            scored_content.append(content)
        
        # Sort by score
        scored_content.sort(key=lambda x: x["score"], reverse=True)
        
        return scored_content
    
    def _get_writing_tips(self, platform: str) -> List[str]:
        """Get platform-specific writing tips"""
        tips = {
            "twitter": [
                "Start with a strong hook in the first 7 words",
                "Use line breaks for better readability",
                "End with a question to drive replies",
                "Consider thread format for complex topics"
            ],
            "linkedin": [
                "Open with a personal story or observation",
                "Use short paragraphs (2-3 sentences max)",
                "Include data or statistics when possible",
                "End with a clear call-to-action"
            ],
            "instagram": [
                "Front-load key message in first 125 characters",
                "Use emojis strategically for visual breaks",
                "Include a clear CTA",
                "Consider carousel format for educational content"
            ]
        }
        
        return tips.get(platform.lower(), [
            "Know your audience",
            "Be authentic and genuine",
            "Provide value in every post"
        ])