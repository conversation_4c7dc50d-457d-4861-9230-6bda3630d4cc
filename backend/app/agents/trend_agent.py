"""
Trend Analysis Agent - Identifies and leverages trending topics
"""
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio
import re

from app.agents.base import BaseAgent, AgentContext
from app.services.perplexity_service import perplexity_service
from app.core.redis_client import redis_client
from app.core.cache import cache_result


class TrendAnalysisAgent(BaseAgent):
    """Agent responsible for identifying and analyzing trends"""
    
    def __init__(self):
        super().__init__(
            name="TrendAnalysisAgent",
            description="Identifies trending topics and viral content opportunities"
        )
        self.perplexity = perplexity_service
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """Analyze trends related to the topic"""
        try:
            # Get current trends
            current_trends = await self._get_current_trends(
                context.platform,
                context.topic
            )
            
            # Analyze viral patterns
            viral_patterns = await self._analyze_viral_patterns(
                context.platform,
                context.topic
            )
            
            # Identify trend opportunities
            opportunities = self._identify_opportunities(
                current_trends,
                viral_patterns,
                context.topic
            )
            
            # Generate trend-based content angles
            content_angles = self._generate_trend_angles(
                opportunities,
                context.platform
            )
            
            # Predict trend lifecycle
            trend_predictions = self._predict_trend_lifecycle(
                current_trends,
                context.platform
            )
            
            return {
                "status": "success",
                "output": {
                    "current_trends": current_trends,
                    "viral_patterns": viral_patterns,
                    "opportunities": opportunities,
                    "content_angles": content_angles,
                    "trend_predictions": trend_predictions,
                    "recommendations": self._generate_recommendations(
                        opportunities,
                        context.platform
                    )
                },
                "metadata": {
                    "trends_analyzed": len(current_trends),
                    "opportunities_found": len(opportunities),
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    @cache_result(expire=1800, prefix="trends")
    async def _get_current_trends(
        self,
        platform: str,
        topic: str
    ) -> List[Dict[str, Any]]:
        """Get current trending topics"""
        # Platform-specific trend queries
        queries = {
            "twitter": f"trending {topic} twitter viral tweets hashtags",
            "linkedin": f"trending {topic} linkedin professional discussions",
            "instagram": f"trending {topic} instagram reels hashtags",
            "tiktok": f"trending {topic} tiktok viral challenges"
        }
        
        query = queries.get(platform.lower(), f"trending {topic} social media")
        
        try:
            # Get trending topics
            trending_data = await self.perplexity.research_topic(
                query,
                additional_context="Focus on last 7 days trends"
            )
            
            # Extract trends
            trends = []
            findings = trending_data.get("findings", [])
            
            for finding in findings:
                trend = self._extract_trend_info(finding, platform)
                if trend:
                    trends.append(trend)
            
            # Get hashtag trends
            hashtag_trends = await self._get_hashtag_trends(topic, platform)
            trends.extend(hashtag_trends)
            
            return trends[:10]  # Top 10 trends
            
        except Exception:
            return []
    
    def _extract_trend_info(
        self,
        finding: str,
        platform: str
    ) -> Optional[Dict[str, Any]]:
        """Extract trend information from a finding"""
        trend_info = {
            "description": finding,
            "platform": platform,
            "relevance_score": 0,
            "trend_type": "general"
        }
        
        # Look for hashtags
        hashtags = re.findall(r'#\w+', finding)
        if hashtags:
            trend_info["hashtags"] = hashtags
            trend_info["trend_type"] = "hashtag"
            trend_info["relevance_score"] += 20
        
        # Look for viral indicators
        viral_keywords = ["viral", "trending", "popular", "million views", "breaking"]
        for keyword in viral_keywords:
            if keyword in finding.lower():
                trend_info["trend_type"] = "viral"
                trend_info["relevance_score"] += 30
                break
        
        # Look for engagement metrics
        if any(char.isdigit() for char in finding):
            numbers = re.findall(r'\d+[KMB]?', finding)
            if numbers:
                trend_info["metrics"] = numbers
                trend_info["relevance_score"] += 15
        
        return trend_info if trend_info["relevance_score"] > 0 else None
    
    async def _get_hashtag_trends(
        self,
        topic: str,
        platform: str
    ) -> List[Dict[str, Any]]:
        """Get trending hashtags related to the topic"""
        # This would normally use platform APIs
        # For now, return common patterns
        base_hashtags = {
            "twitter": ["Trending", "Breaking", "Thread", "TwitterX"],
            "linkedin": ["Leadership", "Innovation", "Growth", "BusinessStrategy"],
            "instagram": ["InstaGood", "PhotoOfTheDay", "Trending", "ViralPost"],
            "tiktok": ["FYP", "ForYou", "Viral", "Trending"]
        }
        
        platform_tags = base_hashtags.get(platform.lower(), [])
        
        hashtag_trends = []
        for tag in platform_tags[:3]:
            hashtag_trends.append({
                "description": f"#{tag} is trending in {topic} discussions",
                "platform": platform,
                "hashtags": [f"#{tag}"],
                "trend_type": "hashtag",
                "relevance_score": 25,
                "estimated_reach": "100K-500K"
            })
        
        return hashtag_trends
    
    async def _analyze_viral_patterns(
        self,
        platform: str,
        topic: str
    ) -> Dict[str, Any]:
        """Analyze what makes content go viral on the platform"""
        viral_patterns = {
            "twitter": {
                "optimal_length": "50-100 characters for max retweets",
                "key_elements": [
                    "Strong emotional hook",
                    "Timely/newsworthy angle",
                    "Clear call-to-action",
                    "Thread format for complex topics"
                ],
                "best_times": ["9 AM EST", "12 PM EST", "5 PM EST"],
                "engagement_triggers": ["Questions", "Controversial takes", "Breaking news"]
            },
            "linkedin": {
                "optimal_length": "1200-1500 characters",
                "key_elements": [
                    "Personal story opener",
                    "Professional insights",
                    "Actionable takeaways",
                    "Thought-provoking questions"
                ],
                "best_times": ["7-9 AM", "12 PM", "5-6 PM on weekdays"],
                "engagement_triggers": ["Career advice", "Success stories", "Industry insights"]
            }
        }
        
        platform_patterns = viral_patterns.get(platform.lower(), {
            "optimal_length": "Varies",
            "key_elements": ["Engaging content", "Visual appeal", "Timeliness"],
            "best_times": ["Morning", "Lunch", "Evening"],
            "engagement_triggers": ["Trending topics", "User questions"]
        })
        
        # Add topic-specific patterns
        if "tech" in topic.lower():
            platform_patterns["topic_specific"] = [
                "Product launches",
                "Innovation announcements",
                "Technical tutorials"
            ]
        elif "business" in topic.lower():
            platform_patterns["topic_specific"] = [
                "Market insights",
                "Leadership lessons",
                "Growth strategies"
            ]
        
        return platform_patterns
    
    def _identify_opportunities(
        self,
        trends: List[Dict[str, Any]],
        viral_patterns: Dict[str, Any],
        topic: str
    ) -> List[Dict[str, Any]]:
        """Identify content opportunities from trends"""
        opportunities = []
        
        # High-relevance trends
        for trend in trends:
            if trend.get("relevance_score", 0) > 30:
                opportunities.append({
                    "type": "trending_topic",
                    "description": f"Leverage {trend['description']}",
                    "urgency": "high",
                    "potential_reach": trend.get("estimated_reach", "50K+"),
                    "angle": self._suggest_angle(trend, topic),
                    "hashtags": trend.get("hashtags", [])
                })
        
        # Viral format opportunities
        if viral_patterns.get("engagement_triggers"):
            for trigger in viral_patterns["engagement_triggers"][:2]:
                opportunities.append({
                    "type": "viral_format",
                    "description": f"Create content using {trigger} format",
                    "urgency": "medium",
                    "potential_reach": "Varies",
                    "angle": f"Apply {trigger} to {topic}"
                })
        
        # Time-sensitive opportunities
        now = datetime.utcnow()
        if now.weekday() < 5:  # Weekday
            opportunities.append({
                "type": "timing",
                "description": "Post during peak professional hours",
                "urgency": "high",
                "potential_reach": "Maximum audience",
                "angle": "Professional insight on " + topic
            })
        
        return opportunities
    
    def _suggest_angle(self, trend: Dict[str, Any], topic: str) -> str:
        """Suggest a content angle based on trend"""
        trend_type = trend.get("trend_type", "general")
        
        if trend_type == "viral":
            return f"Join the conversation about {topic} with a unique perspective"
        elif trend_type == "hashtag":
            hashtags = trend.get("hashtags", [])
            return f"Use {hashtags[0] if hashtags else 'trending hashtag'} to share insights on {topic}"
        else:
            return f"Provide expert commentary on {topic} trends"
    
    def _generate_trend_angles(
        self,
        opportunities: List[Dict[str, Any]],
        platform: str
    ) -> List[Dict[str, str]]:
        """Generate specific content angles based on opportunities"""
        angles = []
        
        for opp in opportunities[:5]:  # Top 5 opportunities
            if opp["type"] == "trending_topic":
                angles.append({
                    "angle": opp["angle"],
                    "hook": self._create_hook(opp["description"], platform),
                    "format": self._suggest_format(platform),
                    "urgency": opp["urgency"]
                })
            elif opp["type"] == "viral_format":
                angles.append({
                    "angle": f"Controversial take on {opp['angle']}",
                    "hook": f"Unpopular opinion: {opp['description'][:50]}...",
                    "format": "thread" if platform.lower() == "twitter" else "article",
                    "urgency": opp["urgency"]
                })
        
        return angles
    
    def _create_hook(self, description: str, platform: str) -> str:
        """Create an engaging hook based on the opportunity"""
        hooks = {
            "twitter": [
                f"🚨 Breaking: {description[:40]}...",
                f"Thread: Everything you need to know about {description[:30]}",
                f"Hot take: {description[:40]}..."
            ],
            "linkedin": [
                f"After 10 years in the industry, here's my take on {description[:30]}",
                f"The {description[:30]} trend is reshaping our industry. Here's how:",
                f"3 lessons from {description[:40]}"
            ]
        }
        
        platform_hooks = hooks.get(platform.lower(), [f"Insights on {description[:50]}"])
        return platform_hooks[0]  # Return first hook
    
    def _suggest_format(self, platform: str) -> str:
        """Suggest the best content format"""
        formats = {
            "twitter": "thread",
            "linkedin": "article",
            "instagram": "carousel",
            "tiktok": "short video"
        }
        return formats.get(platform.lower(), "post")
    
    def _predict_trend_lifecycle(
        self,
        trends: List[Dict[str, Any]],
        platform: str
    ) -> List[Dict[str, Any]]:
        """Predict how long trends will remain relevant"""
        predictions = []
        
        for trend in trends[:5]:  # Top 5 trends
            # Simple prediction based on trend type
            if trend.get("trend_type") == "viral":
                lifecycle = "24-48 hours"
                peak_time = "Next 6-12 hours"
            elif trend.get("trend_type") == "hashtag":
                lifecycle = "3-7 days"
                peak_time = "Next 1-2 days"
            else:
                lifecycle = "1-2 weeks"
                peak_time = "Next 3-5 days"
            
            predictions.append({
                "trend": trend.get("description", "")[:100],
                "current_stage": self._determine_stage(trend),
                "predicted_lifecycle": lifecycle,
                "peak_time": peak_time,
                "action_recommendation": self._recommend_action(trend)
            })
        
        return predictions
    
    def _determine_stage(self, trend: Dict[str, Any]) -> str:
        """Determine current stage of trend"""
        score = trend.get("relevance_score", 0)
        
        if score > 40:
            return "peak"
        elif score > 25:
            return "rising"
        else:
            return "emerging"
    
    def _recommend_action(self, trend: Dict[str, Any]) -> str:
        """Recommend action based on trend stage"""
        stage = self._determine_stage(trend)
        
        if stage == "peak":
            return "Post immediately to ride the wave"
        elif stage == "rising":
            return "Prepare content for next 24 hours"
        else:
            return "Monitor and prepare for potential growth"
    
    def _generate_recommendations(
        self,
        opportunities: List[Dict[str, Any]],
        platform: str
    ) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Priority recommendations based on opportunities
        high_urgency = [opp for opp in opportunities if opp.get("urgency") == "high"]
        
        if high_urgency:
            recommendations.append(
                f"⚡ Immediate action: {high_urgency[0]['description']}"
            )
        
        # Platform-specific recommendations
        if platform.lower() == "twitter":
            recommendations.extend([
                "🧵 Create a thread to maximize reach",
                "⏰ Post within next 2 hours for optimal visibility",
                "# Use 1-2 trending hashtags maximum"
            ])
        elif platform.lower() == "linkedin":
            recommendations.extend([
                "📊 Include data or statistics for credibility",
                "💡 Share a personal story or lesson learned",
                "🎯 Post on Tuesday-Thursday for best engagement"
            ])
        
        # General recommendations
        recommendations.extend([
            "🔄 Prepare 2-3 variations for A/B testing",
            "📱 Optimize for mobile viewing",
            "💬 End with a question to boost engagement"
        ])
        
        return recommendations[:5]  # Top 5 recommendations