"""
Optimization Agent - Optimizes content for maximum engagement
"""
from typing import Dict, Any, List, Optional
import re
from datetime import datetime, timedelta
import hashlib

from app.agents.base import BaseAgent, AgentContext
from app.core.redis_client import redis_client
from app.core.cache import cache_result


class OptimizationAgent(BaseAgent):
    """Agent responsible for optimizing content for engagement"""
    
    def __init__(self):
        super().__init__(
            name="OptimizationAgent",
            description="Optimizes content for maximum engagement and reach"
        )
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """Optimize content based on platform best practices and analytics"""
        # Extract writing output from context
        writing_output = self._extract_writing_output(context)
        if not writing_output:
            return {
                "status": "error",
                "error": "No writing output found to optimize"
            }
        
        try:
            # Get historical performance data
            performance_data = await self._get_performance_history(
                context.user_id,
                context.platform
            )
            
            # Optimize each variation
            optimized_variations = []
            for variation in writing_output.get("variations", []):
                optimized = await self._optimize_content(
                    variation,
                    context.platform,
                    performance_data
                )
                optimized_variations.append(optimized)
            
            # Generate posting recommendations
            posting_recs = await self._generate_posting_recommendations(
                context.platform,
                context.topic,
                performance_data
            )
            
            # A/B test suggestions
            ab_tests = self._generate_ab_test_suggestions(
                optimized_variations,
                context.platform
            )
            
            return {
                "status": "success",
                "output": {
                    "optimized_content": optimized_variations,
                    "best_variation": self._select_best_variation(optimized_variations),
                    "posting_recommendations": posting_recs,
                    "ab_test_suggestions": ab_tests,
                    "optimization_insights": self._generate_insights(
                        optimized_variations,
                        performance_data
                    )
                },
                "metadata": {
                    "optimizations_applied": self._count_optimizations(optimized_variations),
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _extract_writing_output(self, context: AgentContext) -> Optional[Dict[str, Any]]:
        """Extract writing output from context history"""
        for message in reversed(context.history):
            if message.sender.startswith("WritingAgent") and message.message_type == "result":
                return message.content
        return None
    
    @cache_result(expire=3600, prefix="performance")
    async def _get_performance_history(
        self,
        user_id: int,
        platform: str
    ) -> Dict[str, Any]:
        """Get historical performance data for the user"""
        # This would normally query a database of past post performance
        # For now, return mock data
        return {
            "avg_engagement_rate": 0.045,
            "best_posting_times": ["9:00 AM", "5:00 PM", "8:00 PM"],
            "top_performing_formats": ["questions", "lists", "stories"],
            "successful_hashtags": ["#growth", "#business", "#innovation"],
            "audience_preferences": {
                "content_length": "medium",
                "tone": "professional yet conversational",
                "topics": ["leadership", "productivity", "trends"]
            }
        }
    
    async def _optimize_content(
        self,
        content: Dict[str, Any],
        platform: str,
        performance_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Optimize a single content variation"""
        optimized = content.copy()
        optimizations = []
        
        # Platform-specific optimizations
        if platform.lower() == "twitter":
            optimized["content"], twitter_opts = self._optimize_for_twitter(
                content["content"],
                performance_data
            )
            optimizations.extend(twitter_opts)
            
        elif platform.lower() == "linkedin":
            optimized["content"], linkedin_opts = self._optimize_for_linkedin(
                content["content"],
                performance_data
            )
            optimizations.extend(linkedin_opts)
        
        # Universal optimizations
        optimized["content"], universal_opts = self._apply_universal_optimizations(
            optimized["content"],
            performance_data
        )
        optimizations.extend(universal_opts)
        
        # Add optimization metadata
        optimized["optimizations"] = optimizations
        optimized["optimization_score"] = len(optimizations) * 10
        
        # Update predicted engagement based on optimizations
        if "predicted_engagement" in optimized:
            boost = len(optimizations) * 0.05
            optimized["predicted_engagement"]["overall_score"] = min(
                1.0,
                optimized["predicted_engagement"]["overall_score"] + boost
            )
        
        return optimized
    
    def _optimize_for_twitter(
        self,
        content: str,
        performance_data: Dict[str, Any]
    ) -> tuple[str, List[str]]:
        """Twitter-specific optimizations"""
        optimizations = []
        optimized_content = content
        
        # Add trending hashtags if missing
        if '#' not in content:
            hashtags = performance_data.get("successful_hashtags", [])[:2]
            if hashtags:
                optimized_content += f"\n\n{' '.join(hashtags)}"
                optimizations.append("Added proven hashtags")
        
        # Optimize thread markers
        if len(content) > 200 and "1/" not in content:
            optimized_content = "1/\n\n" + optimized_content
            optimizations.append("Added thread marker for better visibility")
        
        # Add engagement hook
        if not any(content.startswith(hook) for hook in ["🚨", "BREAKING:", "Thread:"]):
            if "important" in content.lower() or "announce" in content.lower():
                optimized_content = "🚨 " + optimized_content
                optimizations.append("Added attention-grabbing emoji")
        
        # Optimize spacing for readability
        if '\n' not in content and len(content) > 140:
            # Add line break at natural points
            sentences = re.split(r'(?<=[.!?])\s+', optimized_content)
            if len(sentences) > 1:
                mid_point = len(sentences) // 2
                optimized_content = ' '.join(sentences[:mid_point]) + '\n\n' + ' '.join(sentences[mid_point:])
                optimizations.append("Added line breaks for readability")
        
        return optimized_content, optimizations
    
    def _optimize_for_linkedin(
        self,
        content: str,
        performance_data: Dict[str, Any]
    ) -> tuple[str, List[str]]:
        """LinkedIn-specific optimizations"""
        optimizations = []
        optimized_content = content
        
        # Add personal touch if missing
        if not any(word in content.lower() for word in ["i", "my", "we", "our"]):
            optimized_content = "I've been thinking about this:\n\n" + optimized_content
            optimizations.append("Added personal opening")
        
        # Ensure proper formatting
        paragraphs = optimized_content.split('\n\n')
        if any(len(p) > 300 for p in paragraphs):
            # Break long paragraphs
            new_paragraphs = []
            for para in paragraphs:
                if len(para) > 300:
                    sentences = re.split(r'(?<=[.!?])\s+', para)
                    mid = len(sentences) // 2
                    new_paragraphs.extend([
                        ' '.join(sentences[:mid]),
                        ' '.join(sentences[mid:])
                    ])
                else:
                    new_paragraphs.append(para)
            optimized_content = '\n\n'.join(new_paragraphs)
            optimizations.append("Improved paragraph structure")
        
        # Add hashtags at the end
        if '#' not in content:
            hashtags = ["#Leadership", "#BusinessGrowth", "#Innovation", "#ProfessionalDevelopment"]
            relevant_tags = [tag for tag in hashtags if tag.lower().replace('#', '') in content.lower()]
            if relevant_tags:
                optimized_content += f"\n\n{' '.join(relevant_tags[:3])}"
                optimizations.append("Added relevant hashtags")
        
        return optimized_content, optimizations
    
    def _apply_universal_optimizations(
        self,
        content: str,
        performance_data: Dict[str, Any]
    ) -> tuple[str, List[str]]:
        """Apply optimizations that work across platforms"""
        optimizations = []
        optimized_content = content
        
        # Add question if missing (drives engagement)
        if '?' not in content and not content.strip().endswith(('!', '...')):
            optimized_content += "\n\nWhat are your thoughts on this?"
            optimizations.append("Added engagement question")
        
        # Optimize emoji usage
        emoji_count = len(re.findall(r'[^\w\s,]', content))
        if emoji_count == 0 and len(content) > 100:
            # Add strategic emojis
            emoji_map = {
                "success": "✅",
                "growth": "📈",
                "idea": "💡",
                "important": "⚡",
                "learn": "🎯"
            }
            for word, emoji in emoji_map.items():
                if word in content.lower():
                    optimized_content = optimized_content.replace(word, f"{word} {emoji}", 1)
                    optimizations.append(f"Added {emoji} emoji for visual appeal")
                    break
        
        # Number formatting for statistics
        def format_number(match):
            num = int(match.group())
            if num >= 1000000:
                return f"{num/1000000:.1f}M"
            elif num >= 1000:
                return f"{num/1000:.1f}K"
            return str(num)
        
        # Find and format large numbers
        number_pattern = r'\b\d{4,}\b'
        if re.search(number_pattern, content):
            optimized_content = re.sub(number_pattern, format_number, optimized_content)
            optimizations.append("Formatted numbers for readability")
        
        return optimized_content, optimizations
    
    async def _generate_posting_recommendations(
        self,
        platform: str,
        topic: str,
        performance_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate recommendations for when and how to post"""
        # Get best posting times from performance data
        best_times = performance_data.get("best_posting_times", [])
        
        # Calculate optimal posting time based on current time
        now = datetime.utcnow()
        next_slots = []
        
        for time_str in best_times:
            # Parse time (simplified - would need proper timezone handling)
            hour = int(time_str.split(':')[0])
            target = now.replace(hour=hour, minute=0, second=0, microsecond=0)
            
            if target < now:
                target += timedelta(days=1)
            
            next_slots.append({
                "time": target.isoformat(),
                "quality": "optimal"
            })
        
        return {
            "best_time_to_post": next_slots[0]["time"] if next_slots else None,
            "alternative_times": next_slots[1:3],
            "avoid_times": ["12:00 AM - 6:00 AM", "2:00 PM - 4:00 PM"],
            "frequency_recommendation": self._get_frequency_recommendation(platform),
            "cross_posting": self._get_cross_posting_recommendations(platform, topic)
        }
    
    def _get_frequency_recommendation(self, platform: str) -> Dict[str, Any]:
        """Get posting frequency recommendations"""
        frequencies = {
            "twitter": {
                "daily": "3-5 posts",
                "weekly": "20-30 posts",
                "note": "Consistency is key, spread throughout the day"
            },
            "linkedin": {
                "daily": "1-2 posts",
                "weekly": "5-10 posts",
                "note": "Quality over quantity, focus on value"
            }
        }
        
        return frequencies.get(platform.lower(), {
            "daily": "1-3 posts",
            "weekly": "7-20 posts",
            "note": "Adjust based on audience engagement"
        })
    
    def _get_cross_posting_recommendations(
        self,
        platform: str,
        topic: str
    ) -> List[Dict[str, str]]:
        """Recommend how to adapt content for other platforms"""
        recommendations = []
        
        if platform.lower() == "twitter":
            recommendations.append({
                "platform": "LinkedIn",
                "adaptation": "Expand into detailed article with professional insights"
            })
            recommendations.append({
                "platform": "Instagram",
                "adaptation": "Create carousel with key points as visuals"
            })
            
        elif platform.lower() == "linkedin":
            recommendations.append({
                "platform": "Twitter",
                "adaptation": "Create thread with main points from the post"
            })
            recommendations.append({
                "platform": "Medium",
                "adaptation": "Expand into full article with examples"
            })
        
        return recommendations
    
    def _generate_ab_test_suggestions(
        self,
        variations: List[Dict[str, Any]],
        platform: str
    ) -> List[Dict[str, Any]]:
        """Generate A/B testing suggestions"""
        if len(variations) < 2:
            return []
        
        tests = []
        
        # Test different hooks
        if variations[0].get("hook") != variations[1].get("hook"):
            tests.append({
                "test_type": "hook",
                "description": "Test different opening hooks",
                "variant_a": variations[0].get("hook", "")[:50],
                "variant_b": variations[1].get("hook", "")[:50],
                "metric": "click-through rate"
            })
        
        # Test content length
        len_a = len(variations[0]["content"])
        len_b = len(variations[1]["content"])
        if abs(len_a - len_b) > 100:
            tests.append({
                "test_type": "length",
                "description": "Test short vs long form content",
                "variant_a": f"{len_a} characters",
                "variant_b": f"{len_b} characters",
                "metric": "engagement rate"
            })
        
        # Test CTA presence
        cta_a = "?" in variations[0]["content"]
        cta_b = "?" in variations[1]["content"]
        if cta_a != cta_b:
            tests.append({
                "test_type": "cta",
                "description": "Test with/without question CTA",
                "variant_a": "With question" if cta_a else "No question",
                "variant_b": "With question" if cta_b else "No question",
                "metric": "comment rate"
            })
        
        return tests[:2]  # Return top 2 test suggestions
    
    def _select_best_variation(
        self,
        variations: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Select the best variation based on optimization score"""
        if not variations:
            return {}
        
        # Sort by optimization score and predicted engagement
        sorted_variations = sorted(
            variations,
            key=lambda x: (
                x.get("optimization_score", 0) +
                x.get("predicted_engagement", {}).get("overall_score", 0) * 100
            ),
            reverse=True
        )
        
        return sorted_variations[0]
    
    def _generate_insights(
        self,
        variations: List[Dict[str, Any]],
        performance_data: Dict[str, Any]
    ) -> List[str]:
        """Generate optimization insights"""
        insights = []
        
        # Analyze optimization patterns
        all_optimizations = []
        for var in variations:
            all_optimizations.extend(var.get("optimizations", []))
        
        if "Added proven hashtags" in all_optimizations:
            insights.append("Adding relevant hashtags can increase reach by 20-30%")
        
        if "Added engagement question" in all_optimizations:
            insights.append("Posts with questions receive 50% more comments on average")
        
        if "Improved paragraph structure" in all_optimizations:
            insights.append("Well-formatted posts have 40% higher read-through rates")
        
        # Performance-based insights
        if performance_data.get("avg_engagement_rate", 0) < 0.03:
            insights.append("Consider testing more conversational tone to boost engagement")
        
        return insights
    
    def _count_optimizations(self, variations: List[Dict[str, Any]]) -> int:
        """Count total optimizations applied"""
        total = 0
        for var in variations:
            total += len(var.get("optimizations", []))
        return total