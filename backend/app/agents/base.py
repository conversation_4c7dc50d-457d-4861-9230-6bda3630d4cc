"""
Base Agent class and interfaces for the multi-agent system
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import asyncio
import json
from uuid import uuid4

from app.core.redis_client import redis_client
from app.core.cache import cache_result


@dataclass
class AgentMessage:
    """Message passed between agents"""
    id: str
    sender: str
    recipient: str
    message_type: str
    content: Any
    metadata: Dict[str, Any]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "sender": self.sender,
            "recipient": self.recipient,
            "message_type": self.message_type,
            "content": self.content,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentMessage':
        return cls(
            id=data["id"],
            sender=data["sender"],
            recipient=data["recipient"],
            message_type=data["message_type"],
            content=data["content"],
            metadata=data["metadata"],
            timestamp=datetime.fromisoformat(data["timestamp"])
        )


@dataclass
class AgentContext:
    """Context shared between agents"""
    user_id: int
    session_id: str
    topic: str
    platform: str
    research_data: Optional[Dict[str, Any]] = None
    additional_context: Optional[str] = None
    constraints: Optional[Dict[str, Any]] = None
    history: List[AgentMessage] = None
    
    def __post_init__(self):
        if self.history is None:
            self.history = []


class BaseAgent(ABC):
    """Base class for all agents in the system"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.id = f"{name}_{uuid4().hex[:8]}"
        self._message_handlers = {}
        self._running = False
    
    @abstractmethod
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """Main processing method for the agent"""
        pass
    
    async def send_message(
        self,
        recipient: str,
        message_type: str,
        content: Any,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Send a message to another agent"""
        message = AgentMessage(
            id=str(uuid4()),
            sender=self.id,
            recipient=recipient,
            message_type=message_type,
            content=content,
            metadata=metadata or {},
            timestamp=datetime.utcnow()
        )
        
        # Publish to Redis for inter-agent communication
        await redis_client.publish(
            f"agent:{recipient}",
            message.to_dict()
        )
        
        return message
    
    async def broadcast(
        self,
        message_type: str,
        content: Any,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Broadcast a message to all agents"""
        await self.send_message(
            "*",  # Special recipient for broadcast
            message_type,
            content,
            metadata
        )
    
    def on_message(self, message_type: str):
        """Decorator to register message handlers"""
        def decorator(func):
            self._message_handlers[message_type] = func
            return func
        return decorator
    
    async def handle_message(self, message: AgentMessage):
        """Handle incoming messages"""
        handler = self._message_handlers.get(message.message_type)
        if handler:
            await handler(message)
    
    async def start(self):
        """Start the agent's message listener"""
        self._running = True
        
        # Subscribe to agent's channel
        pubsub = await redis_client.subscribe(f"agent:{self.id}", "agent:*")
        
        async for message in pubsub.listen():
            if not self._running:
                break
                
            if message["type"] == "message":
                try:
                    agent_message = AgentMessage.from_dict(
                        json.loads(message["data"])
                    )
                    
                    # Check if message is for this agent or broadcast
                    if agent_message.recipient in [self.id, "*"]:
                        await self.handle_message(agent_message)
                        
                except Exception as e:
                    print(f"Error handling message in {self.name}: {e}")
    
    async def stop(self):
        """Stop the agent"""
        self._running = False
    
    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "running": self._running
        }


class AgentOrchestrator:
    """Orchestrates multiple agents for complex tasks"""
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.workflows: Dict[str, List[str]] = {}
    
    def register_agent(self, agent: BaseAgent):
        """Register an agent with the orchestrator"""
        self.agents[agent.name] = agent
    
    def register_workflow(self, name: str, agent_sequence: List[str]):
        """Register a workflow as a sequence of agents"""
        self.workflows[name] = agent_sequence
    
    async def execute_workflow(
        self,
        workflow_name: str,
        context: AgentContext
    ) -> Dict[str, Any]:
        """Execute a registered workflow"""
        if workflow_name not in self.workflows:
            raise ValueError(f"Unknown workflow: {workflow_name}")
        
        agent_sequence = self.workflows[workflow_name]
        results = {}
        
        for agent_name in agent_sequence:
            if agent_name not in self.agents:
                raise ValueError(f"Unknown agent: {agent_name}")
            
            agent = self.agents[agent_name]
            
            # Process with the agent
            result = await agent.process(context)
            results[agent_name] = result
            
            # Update context with results for next agent
            if "output" in result:
                context.history.append(
                    AgentMessage(
                        id=str(uuid4()),
                        sender=agent.id,
                        recipient="orchestrator",
                        message_type="result",
                        content=result["output"],
                        metadata={"agent": agent_name},
                        timestamp=datetime.utcnow()
                    )
                )
        
        return {
            "workflow": workflow_name,
            "results": results,
            "final_output": results.get(agent_sequence[-1], {}).get("output")
        }
    
    async def execute_parallel(
        self,
        agent_names: List[str],
        context: AgentContext
    ) -> Dict[str, Any]:
        """Execute multiple agents in parallel"""
        tasks = []
        
        for agent_name in agent_names:
            if agent_name not in self.agents:
                raise ValueError(f"Unknown agent: {agent_name}")
            
            agent = self.agents[agent_name]
            tasks.append(agent.process(context))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            agent_names[i]: result if not isinstance(result, Exception) else {
                "error": str(result)
            }
            for i, result in enumerate(results)
        }


# Global orchestrator instance
orchestrator = AgentOrchestrator()