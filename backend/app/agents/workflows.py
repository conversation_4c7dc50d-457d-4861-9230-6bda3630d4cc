"""
Pre-defined workflows for the multi-agent system
"""
from app.agents.base import orchestrator
from app.agents.research_agent import ResearchAgent
from app.agents.writing_agent import WritingAgent
from app.agents.optimization_agent import OptimizationAgent
from app.agents.trend_agent import TrendAnalysisAgent


def register_agents():
    """Register all agents with the orchestrator"""
    # Create agent instances
    research_agent = ResearchAgent()
    writing_agent = WritingAgent()
    optimization_agent = OptimizationAgent()
    trend_agent = TrendAnalysisAgent()
    
    # Register agents
    orchestrator.register_agent(research_agent)
    orchestrator.register_agent(writing_agent)
    orchestrator.register_agent(optimization_agent)
    orchestrator.register_agent(trend_agent)
    
    # Register workflows
    register_workflows()


def register_workflows():
    """Register pre-defined workflows"""
    
    # Standard content generation workflow
    orchestrator.register_workflow(
        "standard_content",
        ["ResearchAgent", "WritingAgent", "OptimizationAgent"]
    )
    
    # Trend-based content workflow
    orchestrator.register_workflow(
        "trending_content",
        ["TrendAnalysisAgent", "ResearchAgent", "WritingAgent", "OptimizationAgent"]
    )
    
    # Quick content (no research)
    orchestrator.register_workflow(
        "quick_content",
        ["WritingAgent", "OptimizationAgent"]
    )
    
    # Research only workflow
    orchestrator.register_workflow(
        "research_only",
        ["ResearchAgent"]
    )
    
    # Trend analysis workflow
    orchestrator.register_workflow(
        "trend_analysis",
        ["TrendAnalysisAgent"]
    )
    
    # Full analysis workflow
    orchestrator.register_workflow(
        "full_analysis",
        ["TrendAnalysisAgent", "ResearchAgent"]
    )


# Workflow execution helpers
async def generate_standard_content(context):
    """Generate content using standard workflow"""
    return await orchestrator.execute_workflow("standard_content", context)


async def generate_trending_content(context):
    """Generate content based on trends"""
    return await orchestrator.execute_workflow("trending_content", context)


async def generate_quick_content(context):
    """Generate content without research"""
    return await orchestrator.execute_workflow("quick_content", context)


async def analyze_topic(context):
    """Analyze a topic for content opportunities"""
    return await orchestrator.execute_workflow("full_analysis", context)


# Initialize agents on module import
register_agents()