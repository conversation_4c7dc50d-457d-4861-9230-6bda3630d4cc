#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create a default user for the SocialAI Pro application
Creates user: <EMAIL> with password: "admin"
"""

import asyncio
import sys
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

# Add the app directory to Python path
sys.path.append('/Users/<USER>/Downloads/claude-code/social media/backend')

from app.models.database import AsyncSessionLocal, init_db
from app.models.user import User
from app.models.content import Post, ScheduledPost  # Import these to resolve relationships
from app.core.security import get_password_hash


async def create_default_user():
    """Create the default user if it doesn't exist"""
    
    # Initialize database
    await init_db()
    
    # Create database session
    async with AsyncSessionLocal() as db:
        try:
            # Check if user already exists
            stmt = select(User).where(User.email == "<EMAIL>")
            result = await db.execute(stmt)
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                print("✅ User <EMAIL> already exists!")
                print(f"   - Username: {existing_user.username}")
                print(f"   - Active: {existing_user.is_active}")
                print(f"   - Superuser: {existing_user.is_superuser}")
                return existing_user
            
            # Hash the password
            hashed_password = get_password_hash("admin")
            
            # Create new user
            new_user = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=hashed_password,
                is_active=True,
                is_superuser=True  # Making this user a superuser for admin access
            )
            
            # Save to database
            db.add(new_user)
            await db.commit()
            await db.refresh(new_user)
            
            print("🎉 Default user created successfully!")
            print(f"   - Email: {new_user.email}")
            print(f"   - Username: {new_user.username}")
            print(f"   - Password: admin")
            print(f"   - Active: {new_user.is_active}")
            print(f"   - Superuser: {new_user.is_superuser}")
            print(f"   - Created at: {new_user.created_at}")
            
            return new_user
            
        except Exception as e:
            print(f"❌ Error creating user: {e}")
            await db.rollback()
            return None


async def main():
    """Main function"""
    print("🚀 Creating default user for SocialAI Pro...")
    print("=" * 50)
    
    user = await create_default_user()
    
    if user:
        print("\n✅ Setup complete!")
        print("\nYou can now login with:")
        print("   Email: <EMAIL>")
        print("   Password: admin")
    else:
        print("\n❌ Failed to create default user")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)