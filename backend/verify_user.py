#!/usr/bin/env python3
"""
Script to verify the default user credentials
Tests <NAME_EMAIL> and password "password"
"""

import asyncio
import sys
from sqlalchemy.ext.asyncio import AsyncSession

# Add the app directory to Python path
sys.path.append('/Users/<USER>/Downloads/Claude Code/social media/backend')

from app.models.database import AsyncSessionLocal
from app.models.user import User
from app.models.content import Post, ScheduledPost  # Import these to resolve relationships
from app.core.security import authenticate_user, get_password_hash, verify_password


async def verify_user_credentials():
    """Verify user credentials and update password if needed"""
    
    # Create database session
    async with AsyncSessionLocal() as db:
        try:
            # Test authentication
            user = await authenticate_user(db, "<EMAIL>", "password")
            
            if user:
                print("✅ Authentication successful!")
                print(f"   - Email: {user.email}")
                print(f"   - Username: {user.username}")
                print(f"   - Active: {user.is_active}")
                print(f"   - Superuser: {user.is_superuser}")
                return True
            else:
                print("❌ Authentication failed with current password")
                print("🔄 Attempting to reset password to 'password'...")
                
                # Get user by email
                from sqlalchemy import select
                stmt = select(User).where(User.email == "<EMAIL>")
                result = await db.execute(stmt)
                user = result.scalar_one_or_none()
                
                if user:
                    # Update password
                    user.hashed_password = get_password_hash("password")
                    await db.commit()
                    
                    print("✅ Password reset successful!")
                    print("   - New password: password")
                    
                    # Test authentication again
                    user = await authenticate_user(db, "<EMAIL>", "password")
                    if user:
                        print("✅ Authentication now works!")
                        return True
                    else:
                        print("❌ Authentication still fails")
                        return False
                else:
                    print("❌ User not found")
                    return False
                    
        except Exception as e:
            print(f"❌ Error: {e}")
            return False


async def main():
    """Main function"""
    print("🔍 Verifying user credentials...")
    print("=" * 40)
    
    success = await verify_user_credentials()
    
    if success:
        print("\n✅ User verification complete!")
        print("\nLogin credentials:")
        print("   Email: <EMAIL>")
        print("   Password: password")
    else:
        print("\n❌ User verification failed")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)