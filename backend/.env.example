# Application
SECRET_KEY=your-super-secret-key-here
PROJECT_NAME=SocialAI Pro
VERSION=1.0.0

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/socialaipro

# Redis
REDIS_URL=redis://localhost:6379

# CORS Origins (comma separated)
BAC<PERSON><PERSON>_CORS_ORIGINS=http://localhost:3000,https://localhost:3000

# AI API Keys
ANTHROPIC_API_KEY=your-anthropic-api-key-here
PERPLEXITY_API_KEY=your-perplexity-api-key-here

# Twitter/X API (optional - can be set per user)
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
TWITTER_ACCESS_TOKEN=your-twitter-access-token
TWITTER_ACCESS_TOKEN_SECRET=your-twitter-access-token-secret

# LinkedIn API (optional - can be set per user)
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Celery
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# AI Model Settings
CLAUDE_MODEL=claude-4-sonnet-20250514
PERPLEXITY_MODEL=sonar-pro

# Security
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60