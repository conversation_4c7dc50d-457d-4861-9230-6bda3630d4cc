import { useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContextV2';

const WS_URL = process.env.REACT_APP_WS_URL || 'ws://localhost:8000';

export const useWebSocket = (endpoint = '/ws', options = {}) => {
  const { getStoredTokens } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState(null);
  const [error, setError] = useState(null);
  
  const ws = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const messageHandlersRef = useRef({});
  const reconnectAttempts = useRef(0);
  
  const {
    onOpen = () => {},
    onClose = () => {},
    onError = () => {},
    onMessage = () => {},
    reconnect = true,
    reconnectInterval = 5000,
    maxReconnectAttempts = 10
  } = options;
  
  // Register message handler
  const on = useCallback((type, handler) => {
    if (!messageHandlersRef.current[type]) {
      messageHandlersRef.current[type] = [];
    }
    messageHandlersRef.current[type].push(handler);
    
    // Return unsubscribe function
    return () => {
      messageHandlersRef.current[type] = messageHandlersRef.current[type].filter(
        h => h !== handler
      );
    };
  }, []);
  
  // Send message
  const send = useCallback((message) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);
  
  // Send typed message
  const sendTypedMessage = useCallback((type, data = {}) => {
    return send({ type, ...data });
  }, [send]);
  
  // Connect to WebSocket
  const connect = useCallback(() => {
    const { accessToken } = getStoredTokens();
    
    if (!accessToken) {
      setError('No authentication token');
      return;
    }
    
    // Clean up existing connection
    if (ws.current) {
      ws.current.close();
    }
    
    // Create new WebSocket connection
    const wsUrl = `${WS_URL}${endpoint}?token=${accessToken}`;
    ws.current = new WebSocket(wsUrl);
    
    ws.current.onopen = (event) => {
      console.log('WebSocket connected');
      setIsConnected(true);
      setError(null);
      reconnectAttempts.current = 0;
      onOpen(event);
    };
    
    ws.current.onclose = (event) => {
      console.log('WebSocket disconnected');
      setIsConnected(false);
      onClose(event);
      
      // Handle reconnection
      if (
        reconnect &&
        !event.wasClean &&
        reconnectAttempts.current < maxReconnectAttempts
      ) {
        reconnectAttempts.current++;
        console.log(`Reconnecting... (attempt ${reconnectAttempts.current})`);
        
        reconnectTimeoutRef.current = setTimeout(() => {
          connect();
        }, reconnectInterval);
      }
    };
    
    ws.current.onerror = (event) => {
      console.error('WebSocket error:', event);
      setError('WebSocket connection error');
      onError(event);
    };
    
    ws.current.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        setLastMessage(message);
        
        // Call global message handler
        onMessage(message);
        
        // Call type-specific handlers
        const handlers = messageHandlersRef.current[message.type] || [];
        handlers.forEach(handler => handler(message));
        
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };
  }, [
    endpoint,
    getStoredTokens,
    onOpen,
    onClose,
    onError,
    onMessage,
    reconnect,
    reconnectInterval,
    maxReconnectAttempts
  ]);
  
  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (ws.current) {
      ws.current.close();
    }
  }, []);
  
  // Set up connection on mount
  useEffect(() => {
    connect();
    
    // Set up ping interval
    const pingInterval = setInterval(() => {
      sendTypedMessage('ping');
    }, 30000);
    
    return () => {
      clearInterval(pingInterval);
      disconnect();
    };
  }, [connect, disconnect, sendTypedMessage]);
  
  return {
    isConnected,
    lastMessage,
    error,
    send,
    sendTypedMessage,
    on,
    connect,
    disconnect
  };
};

// Specialized hooks for different features
export const useContentCollaboration = (postId) => {
  return useWebSocket(`/ws/content/${postId}`, {
    onOpen: () => console.log(`Joined content collaboration for post ${postId}`),
    onClose: () => console.log(`Left content collaboration for post ${postId}`)
  });
};

export const useAnalyticsWebSocket = () => {
  return useWebSocket('/ws/analytics', {
    onOpen: () => console.log('Connected to analytics stream'),
    onClose: () => console.log('Disconnected from analytics stream')
  });
};

export const useNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  
  const ws = useWebSocket('/ws/notifications', {
    onMessage: (message) => {
      if (message.type === 'notification') {
        setNotifications(prev => [message.data, ...prev]);
        setUnreadCount(prev => prev + 1);
      } else if (message.type === 'notifications.unread_count') {
        setUnreadCount(message.count);
      }
    }
  });
  
  const markAsRead = useCallback((notificationId) => {
    ws.sendTypedMessage('notification.mark_read', { id: notificationId });
    setUnreadCount(prev => Math.max(0, prev - 1));
  }, [ws]);
  
  return {
    ...ws,
    notifications,
    unreadCount,
    markAsRead
  };
};