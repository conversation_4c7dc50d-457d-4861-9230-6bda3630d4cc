import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import api, { authAPI } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Token management
  const getStoredTokens = () => {
    return {
      accessToken: localStorage.getItem('access_token'),
      refreshToken: localStorage.getItem('refresh_token')
    };
  };

  const storeTokens = (accessToken, refreshToken) => {
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
  };

  const clearTokens = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  };

  // Refresh token logic
  const refreshAccessToken = useCallback(async () => {
    const { refreshToken } = getStoredTokens();
    
    if (!refreshToken || isRefreshing) {
      return null;
    }

    setIsRefreshing(true);
    
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/auth/refresh`,
        { refresh_token: refreshToken }
      );
      
      const { access_token, refresh_token } = response.data;
      storeTokens(access_token, refresh_token);
      
      return access_token;
    } catch (error) {
      clearTokens();
      setUser(null);
      return null;
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing]);

  // Setup axios interceptors
  useEffect(() => {
    // Request interceptor
    const requestInterceptor = api.interceptors.request.use(
      (config) => {
        const { accessToken } = getStoredTokens();
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    const responseInterceptor = api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          const newAccessToken = await refreshAccessToken();
          
          if (newAccessToken) {
            originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
            return api(originalRequest);
          }
        }

        return Promise.reject(error);
      }
    );

    return () => {
      api.interceptors.request.eject(requestInterceptor);
      api.interceptors.response.eject(responseInterceptor);
    };
  }, [refreshAccessToken]);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      const { accessToken } = getStoredTokens();
      
      if (accessToken) {
        try {
          const response = await authAPI.getCurrentUser();
          setUser(response.data);
        } catch (error) {
          // Try refreshing the token
          const newAccessToken = await refreshAccessToken();
          
          if (newAccessToken) {
            try {
              const response = await authAPI.getCurrentUser();
              setUser(response.data);
            } catch (refreshError) {
              clearTokens();
            }
          }
        }
      }
      
      setLoading(false);
    };

    initializeAuth();
  }, [refreshAccessToken]);

  // Auth methods
  const login = async (username, password, rememberMe = false, deviceInfo = null) => {
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/auth/login`,
        {
          username,
          password,
          remember_me: rememberMe,
          device_info: deviceInfo || {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language
          }
        }
      );
      
      const { access_token, refresh_token } = response.data;
      storeTokens(access_token, refresh_token);
      
      // Get user data
      const userResponse = await authAPI.getCurrentUser();
      setUser(userResponse.data);
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || 'Login failed'
      };
    }
  };

  const register = async (userData) => {
    try {
      await authAPI.register(userData);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || 'Registration failed'
      };
    }
  };

  const logout = async () => {
    const { refreshToken } = getStoredTokens();
    
    if (refreshToken) {
      try {
        await api.post('/api/auth/logout', { token: refreshToken });
      } catch (error) {
        console.error('Logout error:', error);
      }
    }
    
    clearTokens();
    setUser(null);
  };

  const logoutAllDevices = async () => {
    try {
      await api.post('/api/auth/logout-all');
      clearTokens();
      setUser(null);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || 'Failed to logout from all devices'
      };
    }
  };

  const value = {
    user,
    loading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    logoutAllDevices,
    refreshAccessToken,
    getStoredTokens
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};