import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Button,
  Collapse,
  IconButton,
} from '@mui/material';
import {
  CheckCircle,
  RadioButtonUnchecked,
  Error,
  ExpandMore,
  ExpandLess,
  Refresh,
} from '@mui/icons-material';

const ProgressTracker = ({ 
  operationId, 
  onComplete, 
  onError, 
  autoConnect = true,
  showDetails = true 
}) => {
  const [progress, setProgress] = useState({
    step: 0,
    total_steps: 1,
    progress_percent: 0,
    status: 'starting',
    message: 'Initializing...',
    elapsed_time: 0,
    data: {}
  });
  const [messages, setMessages] = useState([]);
  const [expanded, setExpanded] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);

  // WebSocket connection
  useEffect(() => {
    if (!operationId || !autoConnect) return;

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [operationId, autoConnect]);

  const connectWebSocket = () => {
    try {
      const token = localStorage.getItem('access_token');
      const wsUrl = `ws://localhost:8000/api/ws/progress?token=${token}`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('✅ WebSocket connected for progress tracking');
        setConnectionStatus('connected');
        
        // Subscribe to operation updates
        wsRef.current.send(JSON.stringify({
          type: 'subscribe_to_operation',
          operation_id: operationId
        }));
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('❌ WebSocket disconnected:', event.code, event.reason);
        setConnectionStatus('disconnected');
        
        // Attempt to reconnect after 3 seconds
        if (event.code !== 1000) { // Not a normal closure
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log('🔄 Attempting to reconnect WebSocket...');
            connectWebSocket();
          }, 3000);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');
      };

    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      setConnectionStatus('error');
    }
  };

  const handleWebSocketMessage = (data) => {
    if (data.type === 'progress_update' && data.operation_id === operationId) {
      const newProgress = {
        step: data.step,
        total_steps: data.total_steps,
        progress_percent: data.progress_percent,
        status: data.status,
        message: data.message,
        elapsed_time: data.elapsed_time,
        data: data.data || {}
      };

      setProgress(newProgress);

      // Add message to history
      setMessages(prev => [...prev, {
        step: data.step,
        message: data.message,
        timestamp: data.timestamp,
        status: data.status
      }].slice(-10)); // Keep last 10 messages

      // Handle completion
      if (data.status === 'completed') {
        onComplete?.(newProgress);
      } else if (data.status === 'error') {
        onError?.(data.message, data.data?.error_details);
      }
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'error': return 'error';
      case 'in_progress': return 'primary';
      default: return 'default';
    }
  };

  const getStatusIcon = (status, isCurrentStep = false) => {
    if (status === 'completed') {
      return <CheckCircle color="success" />;
    } else if (status === 'error') {
      return <Error color="error" />;
    } else if (isCurrentStep) {
      return <RadioButtonUnchecked color="primary" />;
    } else {
      return <RadioButtonUnchecked color="disabled" />;
    }
  };

  const formatElapsedTime = (seconds) => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}m ${Math.round(seconds % 60)}s`;
    } else {
      return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
    }
  };

  const handleRetry = () => {
    if (wsRef.current?.readyState === WebSocket.CLOSED) {
      connectWebSocket();
    }
  };

  return (
    <Paper sx={{ p: 3, mb: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">
          Research Progress
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            label={connectionStatus}
            color={connectionStatus === 'connected' ? 'success' : 'error'}
            size="small"
          />
          {connectionStatus !== 'connected' && (
            <IconButton size="small" onClick={handleRetry}>
              <Refresh />
            </IconButton>
          )}
          {showDetails && (
            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
          )}
        </Box>
      </Box>

      {/* Progress Overview */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Step {progress.step} of {progress.total_steps}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {Math.round(progress.progress_percent)}% • {formatElapsedTime(progress.elapsed_time)}
          </Typography>
        </Box>
        
        <LinearProgress
          variant="determinate"
          value={progress.progress_percent}
          sx={{ height: 8, borderRadius: 4, mb: 1 }}
        />
        
        <Typography variant="body1" sx={{ fontWeight: 500 }}>
          {progress.message}
        </Typography>
        
        <Chip
          label={progress.status.replace('_', ' ').toUpperCase()}
          color={getStatusColor(progress.status)}
          size="small"
          sx={{ mt: 1 }}
        />
      </Box>

      {/* Status Alert */}
      {progress.status === 'error' && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Research failed: {progress.message}
          {progress.data?.error_details && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              Details: {progress.data.error_details}
            </Typography>
          )}
        </Alert>
      )}

      {progress.status === 'completed' && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Research completed successfully! 
          {progress.data?.findings_count && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              Found {progress.data.findings_count} key insights in {formatElapsedTime(progress.elapsed_time)}
            </Typography>
          )}
        </Alert>
      )}

      {/* Detailed Progress */}
      {showDetails && (
        <Collapse in={expanded}>
          <Typography variant="subtitle2" sx={{ mb: 1, mt: 2 }}>
            Progress Details
          </Typography>
          
          <List dense>
            {messages.map((msg, index) => (
              <ListItem key={index} sx={{ py: 0.5 }}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  {getStatusIcon(msg.status, msg.step === progress.step)}
                </ListItemIcon>
                <ListItemText
                  primary={msg.message}
                  secondary={new Date(msg.timestamp).toLocaleTimeString()}
                  primaryTypographyProps={{
                    variant: 'body2',
                    color: msg.status === 'error' ? 'error' : 'text.primary'
                  }}
                  secondaryTypographyProps={{
                    variant: 'caption'
                  }}
                />
              </ListItem>
            ))}
          </List>
        </Collapse>
      )}

      {/* Data Display */}
      {progress.data && Object.keys(progress.data).length > 0 && (
        <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary">
            Research Data:
          </Typography>
          <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', mt: 1 }}>
            {JSON.stringify(progress.data, null, 2)}
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default ProgressTracker;