import React, { useState } from 'react';
import {
  UserPlusIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';
import api from '../../services/api';

const CompetitorAnalysis = () => {
  const [competitors, setCompetitors] = useState([]);
  const [newCompetitor, setNewCompetitor] = useState({ username: '', platform: 'instagram' });
  const [loading, setLoading] = useState(false);
  const [selectedCompetitor, setSelectedCompetitor] = useState(null);

  const analyzeCompetitor = async () => {
    if (!newCompetitor.username) return;

    try {
      setLoading(true);
      const response = await api.post('/analytics/competitors/analyze', newCompetitor);
      const analysis = response.data;
      
      setCompetitors([...competitors, analysis]);
      setSelectedCompetitor(analysis);
      setNewCompetitor({ username: '', platform: 'instagram' });
    } catch (error) {
      console.error('Error analyzing competitor:', error);
    } finally {
      setLoading(false);
    }
  };

  const ComparisonBar = ({ label, value, isPositive }) => {
    const percentage = Math.abs(value * 100);
    const isAhead = value > 0;
    
    return (
      <div className="mb-4">
        <div className="flex justify-between text-sm mb-1">
          <span className="text-gray-600">{label}</span>
          <span className={isAhead ? 'text-green-600' : 'text-red-600'}>
            {isAhead ? '+' : '-'}{percentage.toFixed(1)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full ${isAhead ? 'bg-green-500' : 'bg-red-500'}`}
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Add Competitor */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Add Competitor</h2>
        <div className="flex space-x-4">
          <input
            type="text"
            placeholder="Username (e.g., @competitor)"
            value={newCompetitor.username}
            onChange={(e) => setNewCompetitor({ ...newCompetitor, username: e.target.value })}
            className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
          <select
            value={newCompetitor.platform}
            onChange={(e) => setNewCompetitor({ ...newCompetitor, platform: e.target.value })}
            className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="instagram">Instagram</option>
            <option value="twitter">Twitter</option>
            <option value="linkedin">LinkedIn</option>
          </select>
          <button
            onClick={analyzeCompetitor}
            disabled={loading || !newCompetitor.username}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
            ) : (
              <>
                <UserPlusIcon className="h-4 w-4 mr-2" />
                Analyze
              </>
            )}
          </button>
        </div>
      </div>

      {/* Competitor List */}
      {competitors.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Tracked Competitors</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {competitors.map((comp, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedCompetitor(comp)}
                  className={`p-4 rounded-lg border-2 transition-colors ${
                    selectedCompetitor?.competitor.username === comp.competitor.username
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-left">
                    <h3 className="font-medium text-gray-900">{comp.competitor.username}</h3>
                    <p className="text-sm text-gray-500">{comp.competitor.platform}</p>
                    <p className="text-sm text-gray-600 mt-1">
                      {comp.competitor.follower_count.toLocaleString()} followers
                    </p>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Detailed Analysis */}
      {selectedCompetitor && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Performance Comparison */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <ChartBarIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Performance Comparison</h3>
            </div>
            <ComparisonBar
              label="Engagement Rate"
              value={selectedCompetitor.comparison.engagement}
            />
            <ComparisonBar
              label="Growth Rate"
              value={selectedCompetitor.comparison.growth}
            />
            <ComparisonBar
              label="Reach"
              value={selectedCompetitor.comparison.reach}
            />
          </div>

          {/* Content Analysis */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <ArrowTrendingUpIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Content Strategy</h3>
            </div>
            <div className="space-y-3">
              <div>
                <h4 className="text-sm font-medium text-gray-700">Content Themes</h4>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedCompetitor.competitor.content_themes.map((theme, i) => (
                    <span
                      key={i}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {theme}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700">Posting Frequency</h4>
                <p className="text-sm text-gray-600">
                  {selectedCompetitor.competitor.posting_frequency.toFixed(1)} posts/day
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700">Avg. Engagement Rate</h4>
                <p className="text-sm text-gray-600">
                  {selectedCompetitor.competitor.avg_engagement_rate.toFixed(2)}%
                </p>
              </div>
            </div>
          </div>

          {/* Insights */}
          <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
            <div className="flex items-center mb-4">
              <LightBulbIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Insights & Recommendations</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Strengths */}
              <div>
                <h4 className="text-sm font-medium text-green-700 mb-2">Their Strengths</h4>
                <ul className="space-y-1">
                  {selectedCompetitor.insights.strengths.map((strength, i) => (
                    <li key={i} className="text-sm text-gray-600 flex items-start">
                      <span className="text-green-500 mr-2">•</span>
                      {strength}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Opportunities */}
              <div>
                <h4 className="text-sm font-medium text-blue-700 mb-2">Your Opportunities</h4>
                <ul className="space-y-1">
                  {selectedCompetitor.insights.opportunities.map((opp, i) => (
                    <li key={i} className="text-sm text-gray-600 flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      {opp}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Recommendations */}
              <div>
                <h4 className="text-sm font-medium text-purple-700 mb-2">Recommendations</h4>
                <ul className="space-y-1">
                  {selectedCompetitor.recommendations.map((rec, i) => (
                    <li key={i} className="text-sm text-gray-600 flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      {rec}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompetitorAnalysis;