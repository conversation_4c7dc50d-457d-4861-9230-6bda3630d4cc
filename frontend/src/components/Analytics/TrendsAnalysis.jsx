import React, { useState, useEffect } from 'react';
import {
  FireIcon,
  HashtagIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import api from '../../services/api';

const TrendsAnalysis = () => {
  const [trends, setTrends] = useState([]);
  const [selectedPlatform, setSelectedPlatform] = useState('instagram');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTrends();
  }, [selectedPlatform]);

  const fetchTrends = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/analytics/trends/${selectedPlatform}`);
      setTrends(response.data.trends || []);
    } catch (error) {
      console.error('Error fetching trends:', error);
    } finally {
      setLoading(false);
    }
  };

  const getViralityColor = (score) => {
    if (score > 0.8) return 'text-red-600 bg-red-100';
    if (score > 0.6) return 'text-orange-600 bg-orange-100';
    if (score > 0.4) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const getGrowthIcon = (rate) => {
    if (rate > 50) return '🚀';
    if (rate > 25) return '📈';
    if (rate > 10) return '📊';
    return '📉';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Trending Now</h2>
            <p className="text-sm text-gray-500 mt-1">Discover what's hot in your niche</p>
          </div>
          <select
            value={selectedPlatform}
            onChange={(e) => setSelectedPlatform(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="instagram">Instagram</option>
            <option value="twitter">Twitter</option>
            <option value="linkedin">LinkedIn</option>
            <option value="tiktok">TikTok</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <>
          {/* Trending Topics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {trends.map((trend, index) => (
              <div key={index} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <FireIcon className={`h-5 w-5 mr-2 ${getViralityColor(trend.virality_score).split(' ')[0]}`} />
                      <h3 className="font-semibold text-gray-900">{trend.name}</h3>
                    </div>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getViralityColor(trend.virality_score)}`}>
                      {(trend.virality_score * 100).toFixed(0)}% viral
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <ArrowTrendingUpIcon className="h-4 w-4 mr-2" />
                      <span>Growth: {getGrowthIcon(trend.growth_rate)} {trend.growth_rate.toFixed(1)}%</span>
                    </div>

                    <div className="flex items-center text-sm text-gray-600">
                      <HashtagIcon className="h-4 w-4 mr-2" />
                      <span>{trend.volume.toLocaleString()} posts</span>
                    </div>

                    {trend.peak_time && (
                      <div className="flex items-center text-sm text-gray-600">
                        <ClockIcon className="h-4 w-4 mr-2" />
                        <span>Peak: {new Date(trend.peak_time).toLocaleDateString()}</span>
                      </div>
                    )}

                    {/* Related Hashtags */}
                    {trend.related_hashtags && trend.related_hashtags.length > 0 && (
                      <div className="pt-3 border-t border-gray-200">
                        <p className="text-xs font-medium text-gray-700 mb-2">Related hashtags:</p>
                        <div className="flex flex-wrap gap-1">
                          {trend.related_hashtags.slice(0, 3).map((tag, i) => (
                            <span key={i} className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer">
                              #{tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Action Button */}
                  <button className="mt-4 w-full bg-blue-50 text-blue-700 py-2 px-4 rounded-md text-sm font-medium hover:bg-blue-100 transition-colors">
                    Create Content
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* AI Recommendations */}
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <SparklesIcon className="h-6 w-6 text-purple-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">AI-Powered Trend Insights</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">🎯 Quick Wins</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Jump on #MondayMotivation - 85% engagement boost on Mondays</li>
                  <li>• Use Reels for product showcases - 3x more reach than posts</li>
                  <li>• Partner with micro-influencers in the fitness niche</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">📈 Growth Opportunities</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Educational content is trending - create how-to guides</li>
                  <li>• User-generated content campaigns are getting 2x engagement</li>
                  <li>• Live Q&A sessions are growing 45% month-over-month</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Trend Calendar */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Trend Predictions</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="h-2 w-2 bg-green-500 rounded-full mr-3"></div>
                  <div>
                    <p className="font-medium text-gray-900">Earth Day Content</p>
                    <p className="text-sm text-gray-500">Sustainability posts will peak</p>
                  </div>
                </div>
                <span className="text-sm text-gray-600">In 7 days</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="h-2 w-2 bg-blue-500 rounded-full mr-3"></div>
                  <div>
                    <p className="font-medium text-gray-900">Summer Fashion Trends</p>
                    <p className="text-sm text-gray-500">Beach and vacation content surge</p>
                  </div>
                </div>
                <span className="text-sm text-gray-600">In 14 days</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="h-2 w-2 bg-purple-500 rounded-full mr-3"></div>
                  <div>
                    <p className="font-medium text-gray-900">Tech Product Launches</p>
                    <p className="text-sm text-gray-500">Apple event discussions</p>
                  </div>
                </div>
                <span className="text-sm text-gray-600">In 21 days</span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default TrendsAnalysis;