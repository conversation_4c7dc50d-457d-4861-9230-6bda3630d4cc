import React, { useState } from 'react';
import {
  ChartBarIcon,
  LightBulbIcon,
  SparklesIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';
import api from '../../services/api';

const PerformancePredictor = ({ content, platform, onOptimize }) => {
  const [prediction, setPrediction] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showOptimized, setShowOptimized] = useState(false);
  const [optimizedData, setOptimizedData] = useState(null);

  const predictPerformance = async () => {
    if (!content || !platform) return;

    try {
      setLoading(true);
      const response = await api.post('/ml/predict', { content, platform });
      setPrediction(response.data);
    } catch (error) {
      console.error('Error predicting performance:', error);
    } finally {
      setLoading(false);
    }
  };

  const optimizeContent = async () => {
    if (!content || !platform) return;

    try {
      setLoading(true);
      const response = await api.post('/ml/optimize', { content, platform });
      setOptimizedData(response.data);
      setShowOptimized(true);
      if (onOptimize) {
        onOptimize(response.data.optimized_content);
      }
    } catch (error) {
      console.error('Error optimizing content:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPerformanceColor = (score) => {
    if (score > 0.07) return '#10b981'; // green
    if (score > 0.04) return '#f59e0b'; // yellow
    return '#ef4444'; // red
  };

  const getReachColor = (reach) => {
    if (reach > 5000) return '#10b981';
    if (reach > 1000) return '#f59e0b';
    return '#ef4444';
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <BeakerIcon className="h-6 w-6 text-purple-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">AI Performance Prediction</h2>
        </div>
        <button
          onClick={predictPerformance}
          disabled={loading || !content}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:bg-gray-400"
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
          ) : (
            <>
              <ChartBarIcon className="h-4 w-4 mr-2" />
              Analyze
            </>
          )}
        </button>
      </div>

      {prediction && (
        <div className="space-y-6">
          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Engagement Rate */}
            <div className="text-center">
              <div className="w-32 h-32 mx-auto">
                <CircularProgressbar
                  value={prediction.predicted_engagement_rate * 100}
                  maxValue={10}
                  text={`${(prediction.predicted_engagement_rate * 100).toFixed(1)}%`}
                  styles={buildStyles({
                    pathColor: getPerformanceColor(prediction.predicted_engagement_rate),
                    textColor: '#1f2937',
                    trailColor: '#e5e7eb',
                  })}
                />
              </div>
              <p className="mt-2 text-sm font-medium text-gray-900">Engagement Rate</p>
              <p className="text-xs text-gray-500">Industry avg: 3.5%</p>
            </div>

            {/* Predicted Reach */}
            <div className="text-center">
              <div className="w-32 h-32 mx-auto flex items-center justify-center bg-gray-100 rounded-full">
                <div>
                  <p className="text-2xl font-bold" style={{ color: getReachColor(prediction.predicted_reach) }}>
                    {prediction.predicted_reach.toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500">people</p>
                </div>
              </div>
              <p className="mt-2 text-sm font-medium text-gray-900">Predicted Reach</p>
              <p className="text-xs text-gray-500">Based on your audience</p>
            </div>

            {/* Confidence Score */}
            <div className="text-center">
              <div className="w-32 h-32 mx-auto">
                <CircularProgressbar
                  value={prediction.confidence_score * 100}
                  text={`${(prediction.confidence_score * 100).toFixed(0)}%`}
                  styles={buildStyles({
                    pathColor: '#6366f1',
                    textColor: '#1f2937',
                    trailColor: '#e5e7eb',
                  })}
                />
              </div>
              <p className="mt-2 text-sm font-medium text-gray-900">Confidence</p>
              <p className="text-xs text-gray-500">Prediction accuracy</p>
            </div>
          </div>

          {/* Suggestions */}
          {prediction.suggestions && prediction.suggestions.length > 0 && (
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <LightBulbIcon className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="font-medium text-gray-900">Optimization Suggestions</h3>
              </div>
              <ul className="space-y-2">
                {prediction.suggestions.map((suggestion, index) => (
                  <li key={index} className="text-sm text-gray-700 flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    {suggestion}
                  </li>
                ))}
              </ul>
              <button
                onClick={optimizeContent}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <SparklesIcon className="h-4 w-4 mr-2" />
                Apply AI Optimization
              </button>
            </div>
          )}

          {/* Optimized Content */}
          {showOptimized && optimizedData && (
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3">Optimized Content</h3>
              <div className="bg-white rounded p-3 mb-4">
                <p className="text-sm text-gray-700 whitespace-pre-wrap">{optimizedData.optimized_content}</p>
              </div>
              
              {/* Improvement Metrics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white rounded p-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Engagement</span>
                    <div className={`flex items-center text-sm font-medium ${
                      optimizedData.improvement.engagement_rate > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {optimizedData.improvement.engagement_rate > 0 ? (
                        <ArrowUpIcon className="h-4 w-4 mr-1" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 mr-1" />
                      )}
                      {Math.abs(optimizedData.improvement.engagement_rate).toFixed(1)}%
                    </div>
                  </div>
                </div>
                <div className="bg-white rounded p-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Reach</span>
                    <div className={`flex items-center text-sm font-medium ${
                      optimizedData.improvement.reach > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {optimizedData.improvement.reach > 0 ? (
                        <ArrowUpIcon className="h-4 w-4 mr-1" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 mr-1" />
                      )}
                      {Math.abs(optimizedData.improvement.reach).toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Training Status */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          ML model trained on your last 90 days of content • 
          <button className="ml-1 text-blue-600 hover:text-blue-800">Retrain model</button>
        </p>
      </div>
    </div>
  );
};

export default PerformancePredictor;