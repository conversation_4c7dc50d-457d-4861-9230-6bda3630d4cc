import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Paper,
  TextField,
  Typography,
  Avatar,
  AvatarGroup,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Divider,
  Stack
} from '@mui/material';
import {
  Save as SaveIcon,
  Sync as SyncIcon,
  Group as GroupIcon,
  History as HistoryIcon,
  FormatBold as BoldIcon,
  FormatItalic as ItalicIcon,
  Link as LinkIcon,
  EmojiEmotions as EmojiIcon
} from '@mui/icons-material';
import { useContentCollaboration } from '../../hooks/useWebSocket';
import { useAuth } from '../../contexts/AuthContextV2';
import { contentAPI } from '../../services/api';
import debounce from 'lodash/debounce';

const CollaborativeEditor = ({ postId, initialContent = '', onSave }) => {
  const { user } = useAuth();
  const [content, setContent] = useState(initialContent);
  const [localContent, setLocalContent] = useState(initialContent);
  const [participants, setParticipants] = useState([]);
  const [typingUsers, setTypingUsers] = useState(new Set());
  const [savedStatus, setSavedStatus] = useState('saved');
  const [history, setHistory] = useState([]);
  const [showHistory, setShowHistory] = useState(false);
  const textFieldRef = useRef(null);
  const lastUpdateRef = useRef(null);
  
  const {
    isConnected,
    sendTypedMessage,
    on,
    error
  } = useContentCollaboration(postId);

  // Handle incoming content updates
  useEffect(() => {
    const unsubscribeUpdate = on('content_update', (message) => {
      const { data, user_id, timestamp } = message;
      
      // Ignore our own updates
      if (user_id === user.id) return;
      
      // Apply operational transform if needed
      const transformed = applyOperationalTransform(
        localContent,
        content,
        data
      );
      
      setContent(transformed.content);
      setLocalContent(transformed.content);
      lastUpdateRef.current = timestamp;
      
      // Add to history
      setHistory(prev => [...prev, {
        user_id,
        change: data,
        timestamp
      }]);
    });

    const unsubscribeParticipants = on('room.participants', (message) => {
      setParticipants(message.participants);
    });

    const unsubscribeJoin = on('room.user_joined', (message) => {
      setParticipants(prev => [...prev, message.user_id]);
    });

    const unsubscribeLeave = on('room.user_left', (message) => {
      setParticipants(prev => prev.filter(id => id !== message.user_id));
      setTypingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(message.user_id);
        return newSet;
      });
    });

    const unsubscribeTyping = on('typing', (message) => {
      const { user_id, is_typing } = message;
      setTypingUsers(prev => {
        const newSet = new Set(prev);
        if (is_typing) {
          newSet.add(user_id);
        } else {
          newSet.delete(user_id);
        }
        return newSet;
      });
    });

    return () => {
      unsubscribeUpdate();
      unsubscribeParticipants();
      unsubscribeJoin();
      unsubscribeLeave();
      unsubscribeTyping();
    };
  }, [on, user.id, localContent, content]);

  // Debounced content update broadcast
  const broadcastUpdate = useCallback(
    debounce((newContent, cursorPosition) => {
      if (!isConnected) return;
      
      const update = {
        type: 'replace',
        content: newContent,
        cursor: cursorPosition,
        timestamp: Date.now()
      };
      
      sendTypedMessage('content_update', {
        room_id: `content:${postId}`,
        data: update
      });
      
      setSavedStatus('saving');
    }, 300),
    [isConnected, sendTypedMessage, postId]
  );

  // Debounced typing indicator
  const sendTypingIndicator = useCallback(
    debounce((isTyping) => {
      if (!isConnected) return;
      
      sendTypedMessage('typing', {
        room_id: `content:${postId}`,
        is_typing: isTyping
      });
    }, 500),
    [isConnected, sendTypedMessage, postId]
  );

  // Handle content change
  const handleContentChange = (event) => {
    const newContent = event.target.value;
    const cursorPosition = event.target.selectionStart;
    
    setLocalContent(newContent);
    broadcastUpdate(newContent, cursorPosition);
    sendTypingIndicator(true);
    
    // Clear typing indicator after pause
    setTimeout(() => sendTypingIndicator(false), 1500);
  };

  // Save content to server
  const handleSave = async () => {
    setSavedStatus('saving');
    try {
      await contentAPI.updatePost(postId, { content: localContent });
      setSavedStatus('saved');
      if (onSave) onSave(localContent);
    } catch (error) {
      setSavedStatus('error');
      console.error('Failed to save:', error);
    }
  };

  // Auto-save every 30 seconds
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (savedStatus !== 'saved' && localContent !== initialContent) {
        handleSave();
      }
    }, 30000);

    return () => clearInterval(autoSaveInterval);
  }, [localContent, savedStatus, initialContent]);

  // Operational Transform for conflict resolution
  const applyOperationalTransform = (local, remote, update) => {
    // Simplified OT - in production, use a library like OT.js
    if (update.type === 'replace') {
      return { content: update.content };
    }
    
    // For more complex transforms, implement proper OT algorithm
    return { content: remote };
  };

  // Insert formatting
  const insertFormatting = (format) => {
    const textarea = textFieldRef.current;
    if (!textarea) return;
    
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = localContent.substring(start, end);
    
    let formattedText = selectedText;
    switch (format) {
      case 'bold':
        formattedText = `**${selectedText}**`;
        break;
      case 'italic':
        formattedText = `*${selectedText}*`;
        break;
      case 'link':
        formattedText = `[${selectedText}](url)`;
        break;
      case 'emoji':
        // Simple emoji picker - in production use a proper picker
        formattedText = selectedText + ' 🚀';
        break;
      default:
        break;
    }
    
    const newContent = 
      localContent.substring(0, start) + 
      formattedText + 
      localContent.substring(end);
    
    setLocalContent(newContent);
    broadcastUpdate(newContent, start + formattedText.length);
  };

  // Get participant info (mock data - would fetch from server)
  const getParticipantInfo = (userId) => {
    return {
      id: userId,
      name: `User ${userId}`,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userId}`
    };
  };

  return (
    <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
      {/* Header */}
      <Box sx={{ mb: 2 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            Collaborative Editor
          </Typography>
          
          <Stack direction="row" spacing={2} alignItems="center">
            {/* Connection status */}
            <Chip
              icon={<SyncIcon />}
              label={isConnected ? 'Connected' : 'Disconnected'}
              color={isConnected ? 'success' : 'error'}
              size="small"
            />
            
            {/* Participants */}
            <Stack direction="row" spacing={1} alignItems="center">
              <GroupIcon fontSize="small" />
              <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 24, height: 24 } }}>
                {participants.map(userId => {
                  const participant = getParticipantInfo(userId);
                  return (
                    <Tooltip key={userId} title={participant.name}>
                      <Avatar
                        src={participant.avatar}
                        sx={{
                          border: typingUsers.has(userId) ? '2px solid #4CAF50' : 'none'
                        }}
                      />
                    </Tooltip>
                  );
                })}
              </AvatarGroup>
            </Stack>
            
            {/* Actions */}
            <Tooltip title="View history">
              <IconButton onClick={() => setShowHistory(!showHistory)} size="small">
                <HistoryIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Save">
              <IconButton onClick={handleSave} size="small" color="primary">
                {savedStatus === 'saving' ? (
                  <CircularProgress size={20} />
                ) : (
                  <SaveIcon />
                )}
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
        
        {/* Save status */}
        {savedStatus === 'error' && (
          <Alert severity="error" sx={{ mt: 1 }}>
            Failed to save changes
          </Alert>
        )}
      </Box>
      
      <Divider sx={{ mb: 2 }} />
      
      {/* Formatting toolbar */}
      <Box sx={{ mb: 2 }}>
        <Stack direction="row" spacing={1}>
          <IconButton size="small" onClick={() => insertFormatting('bold')}>
            <BoldIcon />
          </IconButton>
          <IconButton size="small" onClick={() => insertFormatting('italic')}>
            <ItalicIcon />
          </IconButton>
          <IconButton size="small" onClick={() => insertFormatting('link')}>
            <LinkIcon />
          </IconButton>
          <IconButton size="small" onClick={() => insertFormatting('emoji')}>
            <EmojiIcon />
          </IconButton>
        </Stack>
      </Box>
      
      {/* Editor */}
      <TextField
        inputRef={textFieldRef}
        fullWidth
        multiline
        rows={12}
        value={localContent}
        onChange={handleContentChange}
        placeholder="Start typing your content..."
        variant="outlined"
        sx={{
          '& .MuiInputBase-root': {
            fontFamily: 'monospace'
          }
        }}
      />
      
      {/* Typing indicators */}
      {typingUsers.size > 0 && (
        <Box sx={{ mt: 1 }}>
          <Typography variant="caption" color="text.secondary">
            {Array.from(typingUsers).map(userId => {
              const participant = getParticipantInfo(userId);
              return participant.name;
            }).join(', ')} {typingUsers.size === 1 ? 'is' : 'are'} typing...
          </Typography>
        </Box>
      )}
      
      {/* Character count */}
      <Box sx={{ mt: 1, display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="caption" color="text.secondary">
          {localContent.length} characters
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Last saved: {savedStatus === 'saved' ? 'Just now' : 'Saving...'}
        </Typography>
      </Box>
      
      {/* History panel */}
      {showHistory && (
        <Paper
          variant="outlined"
          sx={{
            position: 'absolute',
            right: 20,
            top: 100,
            width: 300,
            maxHeight: 400,
            overflow: 'auto',
            p: 2,
            zIndex: 1000
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            Edit History
          </Typography>
          <Divider sx={{ mb: 1 }} />
          {history.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No edits yet
            </Typography>
          ) : (
            <Stack spacing={1}>
              {history.slice(-10).reverse().map((entry, index) => {
                const participant = getParticipantInfo(entry.user_id);
                return (
                  <Box key={index}>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Avatar
                        src={participant.avatar}
                        sx={{ width: 20, height: 20 }}
                      />
                      <Typography variant="caption">
                        {participant.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(entry.timestamp).toLocaleTimeString()}
                      </Typography>
                    </Stack>
                  </Box>
                );
              })}
            </Stack>
          )}
        </Paper>
      )}
      
      {/* Connection error */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          Connection error: {error}
        </Alert>
      )}
    </Paper>
  );
};

export default CollaborativeEditor;