import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Button,
  Skeleton,
  Alert,
  Breadcrumbs,
  Link,
  Chip,
  Stack
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Schedule as ScheduleIcon,
  Publish as PublishIcon,
  Preview as PreviewIcon
} from '@mui/icons-material';
import CollaborativeEditor from '../components/CollaborativeEditor/CollaborativeEditor';
import ContentPreview from '../components/ContentGenerator/ContentPreview';
import { contentAPI, scheduleAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContextV2';

const CollaborativeEdit = () => {
  const { postId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [publishLoading, setPublishLoading] = useState(false);
  
  // Load post data
  useEffect(() => {
    loadPost();
  }, [postId]);
  
  const loadPost = async () => {
    try {
      setLoading(true);
      const response = await contentAPI.getPost(postId);
      setPost(response.data);
    } catch (error) {
      setError('Failed to load post');
      console.error('Error loading post:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSave = async (content) => {
    // Update local state
    setPost(prev => ({ ...prev, content }));
  };
  
  const handleSchedule = () => {
    navigate(`/schedule?postId=${postId}`);
  };
  
  const handlePublishNow = async () => {
    try {
      setPublishLoading(true);
      await scheduleAPI.publishNow(postId);
      // Reload post to get updated status
      await loadPost();
      // Show success message
      alert('Post published successfully!');
    } catch (error) {
      console.error('Error publishing post:', error);
      alert('Failed to publish post');
    } finally {
      setPublishLoading(false);
    }
  };
  
  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Skeleton variant="rectangular" height={600} />
          </Grid>
          <Grid item xs={12} md={4}>
            <Skeleton variant="rectangular" height={400} />
          </Grid>
        </Grid>
      </Container>
    );
  }
  
  if (error || !post) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4 }}>
        <Alert severity="error">
          {error || 'Post not found'}
        </Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard')}
          sx={{ mt: 2 }}
        >
          Back to Dashboard
        </Button>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="xl" sx={{ mt: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            component="button"
            underline="hover"
            color="inherit"
            onClick={() => navigate('/dashboard')}
          >
            Dashboard
          </Link>
          <Link
            component="button"
            underline="hover"
            color="inherit"
            onClick={() => navigate('/generate')}
          >
            Content
          </Link>
          <Typography color="text.primary">Edit</Typography>
        </Breadcrumbs>
        
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h4" gutterBottom>
              Edit Content
            </Typography>
            <Stack direction="row" spacing={1} alignItems="center">
              <Chip
                label={post.platform}
                color="primary"
                size="small"
              />
              <Chip
                label={post.status}
                color={post.status === 'published' ? 'success' : 'default'}
                size="small"
              />
              <Typography variant="body2" color="text.secondary">
                Topic: {post.topic}
              </Typography>
            </Stack>
          </Box>
          
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              startIcon={<PreviewIcon />}
              onClick={() => setShowPreview(!showPreview)}
            >
              {showPreview ? 'Hide' : 'Show'} Preview
            </Button>
            <Button
              variant="outlined"
              startIcon={<ScheduleIcon />}
              onClick={handleSchedule}
              disabled={post.status === 'published'}
            >
              Schedule
            </Button>
            <Button
              variant="contained"
              startIcon={<PublishIcon />}
              onClick={handlePublishNow}
              disabled={post.status === 'published' || publishLoading}
            >
              {publishLoading ? 'Publishing...' : 'Publish Now'}
            </Button>
          </Stack>
        </Stack>
      </Box>
      
      {/* Main content */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={showPreview ? 8 : 12}>
          <CollaborativeEditor
            postId={postId}
            initialContent={post.content}
            onSave={handleSave}
          />
        </Grid>
        
        {showPreview && (
          <Grid item xs={12} md={4}>
            <Paper elevation={3} sx={{ p: 3, position: 'sticky', top: 20 }}>
              <Typography variant="h6" gutterBottom>
                Preview
              </Typography>
              <ContentPreview
                content={post.content}
                platform={post.platform}
                hideActions
              />
              
              {/* Analytics preview */}
              {post.predicted_engagement && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Predicted Performance
                  </Typography>
                  <Stack spacing={1}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Engagement Score
                      </Typography>
                      <Typography variant="h6">
                        {Math.round(post.predicted_engagement.overall_score * 100)}%
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Shareability
                      </Typography>
                      <Typography variant="body1">
                        {Math.round(post.predicted_engagement.shareability * 100)}%
                      </Typography>
                    </Box>
                  </Stack>
                </Box>
              )}
              
              {/* Research insights */}
              {post.research_data && post.research_data.key_insights && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Research Insights Used
                  </Typography>
                  <Stack spacing={1}>
                    {post.research_data.key_insights.slice(0, 3).map((insight, index) => (
                      <Typography key={index} variant="caption" color="text.secondary">
                        • {insight}
                      </Typography>
                    ))}
                  </Stack>
                </Box>
              )}
            </Paper>
          </Grid>
        )}
      </Grid>
      
      {/* Platform tips */}
      <Paper elevation={1} sx={{ mt: 3, p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Platform Tips for {post.platform}
        </Typography>
        <Stack direction="row" spacing={2}>
          {getPlatformTips(post.platform).map((tip, index) => (
            <Chip key={index} label={tip} variant="outlined" size="small" />
          ))}
        </Stack>
      </Paper>
    </Container>
  );
};

// Helper function for platform tips
const getPlatformTips = (platform) => {
  const tips = {
    twitter: [
      'Keep under 280 characters',
      'Use 1-2 hashtags max',
      'Include visuals for 2x engagement',
      'Best times: 9AM, 7-9PM EST'
    ],
    linkedin: [
      'Optimal: 1300-2000 characters',
      'Use 3-5 hashtags',
      'Start with a hook',
      'Post weekdays 7-9AM'
    ]
  };
  
  return tips[platform.toLowerCase()] || ['Create engaging content'];
};

export default CollaborativeEdit;