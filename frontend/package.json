{"name": "socialai-pro-frontend", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-date-pickers": "^6.18.1", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "chart.js": "^4.5.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.19.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.38", "@types/react-dom": "^18.2.17"}, "proxy": "http://localhost:8000"}