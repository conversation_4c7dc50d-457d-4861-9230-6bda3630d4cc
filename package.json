{"name": "socialai-pro", "version": "1.0.0", "description": "AI-powered social media content automation platform", "scripts": {"start": "node scripts/start-with-ports.js", "start:backend": "python3 scripts/find_port.py", "start:frontend": "cd frontend && npm start", "start:simple": "concurrently \"npm run start:backend:auto\" \"npm run start:frontend\"", "start:backend:auto": "cd backend && python3 -c \"import socket; import subprocess; import sys; port = 8000; [port := port + 1 for _ in range(100) if not (lambda p: (lambda s: s.connect_ex(('localhost', p)) != 0)(socket.socket()))(port)]; print(f'🚀 Starting on port {port}'); subprocess.run([sys.executable, '-m', 'uvicorn', 'app.main:app', '--reload', '--host', '0.0.0.0', '--port', str(port)])\"", "start:legacy": "concurrently \"npm run start:backend:legacy\" \"npm run start:frontend\"", "start:backend:legacy": "cd backend && python3 -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "dev": "npm run start", "install:all": "npm run install:backend && npm run install:frontend && npm install", "install:backend": "cd backend && pip3 install -r requirements.txt", "install:frontend": "cd frontend && npm install", "setup": "npm run install:all && npm run setup:env", "setup:env": "cp backend/.env.example backend/.env && cp frontend/.env.example frontend/.env.local", "test": "concurrently \"npm run test:backend\" \"npm run test:frontend\"", "test:backend": "cd backend && pytest", "test:frontend": "cd frontend && npm test -- --watchAll=false", "build": "cd frontend && npm run build", "lint": "concurrently \"npm run lint:backend\" \"npm run lint:frontend\"", "lint:backend": "cd backend && flake8 app/", "lint:frontend": "cd frontend && npm run lint || true"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["social-media", "ai", "automation", "content-generation", "<PERSON><PERSON><PERSON>", "react"], "author": "Your Name", "license": "MIT"}