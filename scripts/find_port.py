#!/usr/bin/env python3
"""
Script to find an available port starting from a given port number.
Usage: python find_port.py [start_port]
"""

import socket
import sys
import subprocess
import os


def is_port_available(port):
    """Check if a port is available"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            return result != 0
    except Exception:
        return False


def find_available_port(start_port=8000, max_attempts=100):
    """Find the next available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        if is_port_available(port):
            return port
    
    raise Exception(f"No available port found in range {start_port}-{start_port + max_attempts}")


def start_backend_with_port(port):
    """Start the backend server with the specified port"""
    print(f"🚀 Starting backend server on port {port}")
    
    # Change to backend directory
    backend_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend')
    
    # Start uvicorn with the found port
    cmd = [
        'python3', '-m', 'uvicorn', 
        'app.main:app', 
        '--reload', 
        '--host', '0.0.0.0', 
        '--port', str(port)
    ]
    
    try:
        subprocess.run(cmd, cwd=backend_dir, check=True)
    except KeyboardInterrupt:
        print(f"\n🛑 Backend server stopped")
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        sys.exit(1)


def main():
    start_port = 8000
    
    # Allow custom start port from command line
    if len(sys.argv) > 1:
        try:
            start_port = int(sys.argv[1])
        except ValueError:
            print("❌ Invalid port number provided")
            sys.exit(1)
    
    try:
        # Find available port
        available_port = find_available_port(start_port)
        
        if available_port != start_port:
            print(f"⚠️  Port {start_port} is busy, using port {available_port} instead")
        
        # Update environment variable for frontend to know backend port
        os.environ['REACT_APP_API_URL'] = f'http://localhost:{available_port}'
        
        # Start the backend
        start_backend_with_port(available_port)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
