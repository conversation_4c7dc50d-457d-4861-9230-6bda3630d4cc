#!/usr/bin/env node
/**
 * <PERSON>ript to start both frontend and backend with dynamic port selection
 */

const { spawn } = require('child_process');
const net = require('net');
const path = require('path');
const fs = require('fs');

// Check if a port is available
function isPortAvailable(port) {
    return new Promise((resolve) => {
        const server = net.createServer();
        
        server.listen(port, () => {
            server.once('close', () => {
                resolve(true);
            });
            server.close();
        });
        
        server.on('error', () => {
            resolve(false);
        });
    });
}

// Find next available port
async function findAvailablePort(startPort = 8000, maxAttempts = 100) {
    for (let port = startPort; port < startPort + maxAttempts; port++) {
        if (await isPortAvailable(port)) {
            return port;
        }
    }
    throw new Error(`No available port found in range ${startPort}-${startPort + maxAttempts}`);
}

// Update frontend environment with backend URL
function updateFrontendEnv(backendPort) {
    const frontendEnvPath = path.join(__dirname, '..', 'frontend', '.env.local');
    const backendUrl = `REACT_APP_API_URL=http://localhost:${backendPort}`;

    try {
        let envContent = '';
        if (fs.existsSync(frontendEnvPath)) {
            envContent = fs.readFileSync(frontendEnvPath, 'utf8');
        }

        // Update or add REACT_APP_API_URL
        const lines = envContent.split('\n').filter(line => line.trim() !== '');
        let found = false;

        for (let i = 0; i < lines.length; i++) {
            if (lines[i].startsWith('REACT_APP_API_URL=')) {
                lines[i] = backendUrl;
                found = true;
                break;
            }
        }

        if (!found) {
            lines.push(backendUrl);
        }

        // Ensure file ends with newline
        fs.writeFileSync(frontendEnvPath, lines.join('\n') + '\n');
        console.log(`✅ Updated frontend environment: ${backendUrl}`);

    } catch (error) {
        console.warn(`⚠️  Could not update frontend environment: ${error.message}`);
    }
}

// Start backend process
function startBackend(port) {
    return new Promise((resolve, reject) => {
        console.log(`🚀 Starting backend server on port ${port}`);
        
        const backend = spawn('python3', [
            '-m', 'uvicorn',
            'app.main:app',
            '--reload',
            '--host', '0.0.0.0',
            '--port', port.toString()
        ], {
            cwd: path.join(__dirname, '..', 'backend'),
            stdio: ['inherit', 'inherit', 'inherit']
        });
        
        backend.on('error', (error) => {
            console.error(`❌ Backend error: ${error.message}`);
            reject(error);
        });
        
        backend.on('exit', (code) => {
            if (code !== 0) {
                console.error(`❌ Backend exited with code ${code}`);
            }
        });
        
        // Give backend time to start
        setTimeout(() => resolve(backend), 2000);
    });
}

// Start frontend process
function startFrontend() {
    return new Promise((resolve, reject) => {
        console.log(`🎨 Starting frontend development server`);
        
        const frontend = spawn('npm', ['start'], {
            cwd: path.join(__dirname, '..', 'frontend'),
            stdio: ['inherit', 'inherit', 'inherit'],
            shell: true
        });
        
        frontend.on('error', (error) => {
            console.error(`❌ Frontend error: ${error.message}`);
            reject(error);
        });
        
        frontend.on('exit', (code) => {
            if (code !== 0) {
                console.error(`❌ Frontend exited with code ${code}`);
            }
        });
        
        resolve(frontend);
    });
}

// Main function
async function main() {
    try {
        // Find available backend port
        const backendPort = await findAvailablePort(8000);
        
        if (backendPort !== 8000) {
            console.log(`⚠️  Port 8000 is busy, using port ${backendPort} for backend instead`);
        }
        
        // Update frontend environment
        updateFrontendEnv(backendPort);
        
        // Start backend
        const backendProcess = await startBackend(backendPort);
        
        // Start frontend
        const frontendProcess = await startFrontend();
        
        console.log(`\n🎉 Application started successfully!`);
        console.log(`📱 Frontend: http://localhost:3000`);
        console.log(`🔧 Backend API: http://localhost:${backendPort}`);
        console.log(`\nPress Ctrl+C to stop both servers\n`);
        
        // Handle graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n🛑 Shutting down servers...');
            
            if (backendProcess) {
                backendProcess.kill('SIGTERM');
            }
            
            if (frontendProcess) {
                frontendProcess.kill('SIGTERM');
            }
            
            setTimeout(() => {
                process.exit(0);
            }, 2000);
        });
        
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main();
}
