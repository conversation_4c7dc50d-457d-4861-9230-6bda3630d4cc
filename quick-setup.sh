#!/bin/bash

echo "🚀 SocialAI Pro - Quick Setup & Deep Research Fix"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_info "Setting up SocialAI Pro with enhanced deep research functionality..."

# 1. Install backend dependencies
print_info "Installing backend dependencies..."
cd backend
if [ ! -d "venv" ]; then
    print_info "Creating Python virtual environment..."
    python3 -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt
print_status "Backend dependencies installed"

# 2. Setup environment files
print_info "Setting up environment files..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    print_warning "Created .env file from example. Please update with your API keys!"
    print_info "Required API keys:"
    print_info "  - ANTHROPIC_API_KEY (for Claude)"
    print_info "  - PERPLEXITY_API_KEY (for research)"
    print_info "  - SECRET_KEY (generate a secure random string)"
else
    print_status "Backend .env file already exists"
fi

cd ..

# 3. Install frontend dependencies
print_info "Installing frontend dependencies..."
cd frontend
npm install
print_status "Frontend dependencies installed"

# Setup frontend environment
if [ ! -f ".env.local" ]; then
    cp .env.example .env.local
    print_status "Created frontend .env.local file"
else
    print_status "Frontend .env.local file already exists"
fi

cd ..

# 4. Install root dependencies
print_info "Installing root dependencies..."
npm install
print_status "Root dependencies installed"

# 5. Database setup
print_info "Setting up database..."
cd backend
source venv/bin/activate

# Check if SQLite database exists, create if not
if [ ! -f "app.db" ]; then
    print_info "Creating SQLite database..."
    python -c "
import asyncio
from app.models.database import init_db

async def setup_db():
    await init_db()
    print('Database initialized successfully')

asyncio.run(setup_db())
"
    print_status "Database created and initialized"
else
    print_status "Database already exists"
fi

cd ..

# 6. Create a test user (optional)
print_info "Would you like to create a test user? (y/n)"
read -r create_user
if [ "$create_user" = "y" ] || [ "$create_user" = "Y" ]; then
    cd backend
    source venv/bin/activate
    python -c "
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.database import get_async_session
from app.models.user import User
from app.core.security import get_password_hash

async def create_test_user():
    async with get_async_session() as db:
        # Check if user already exists
        from sqlalchemy import select
        stmt = select(User).where(User.email == '<EMAIL>')
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            print('Test user already exists: <EMAIL>')
            return
        
        # Create test user
        user = User(
            email='<EMAIL>',
            username='testuser',
            hashed_password=get_password_hash('testpassword123'),
            is_active=True
        )
        db.add(user)
        await db.commit()
        print('Test user created:')
        print('  Email: <EMAIL>')
        print('  Password: testpassword123')

asyncio.run(create_test_user())
"
    cd ..
fi

# 7. Final setup verification
print_info "Verifying setup..."

# Check if required files exist
required_files=(
    "backend/.env"
    "frontend/.env.local"
    "backend/app.db"
    "backend/venv/bin/activate"
    "frontend/node_modules"
    "node_modules"
)

all_good=true
for file in "${required_files[@]}"; do
    if [ -e "$file" ]; then
        print_status "$file exists"
    else
        print_error "$file missing"
        all_good=false
    fi
done

echo ""
echo "🎉 Setup Complete!"
echo "=================="

if [ "$all_good" = true ]; then
    print_status "All components are ready!"
    echo ""
    print_info "To start the application:"
    print_info "  npm start"
    echo ""
    print_info "Or start components separately:"
    print_info "  Backend:  npm run start:backend"
    print_info "  Frontend: npm run start:frontend"
    echo ""
    print_info "The application will be available at:"
    print_info "  Frontend: http://localhost:3000"
    print_info "  Backend:  http://localhost:8000"
    print_info "  API Docs: http://localhost:8000/docs"
    echo ""
    print_warning "Don't forget to update your API keys in backend/.env!"
    print_info "Required keys: ANTHROPIC_API_KEY, PERPLEXITY_API_KEY, SECRET_KEY"
    echo ""
    print_info "🔬 Deep Research is now fully functional with:"
    print_info "  ✅ Real-time progress tracking"
    print_info "  ✅ Multi-phase research workflow"
    print_info "  ✅ WebSocket-based live updates"
    print_info "  ✅ Automatic fallback mechanisms"
    print_info "  ✅ Enhanced error handling"
else
    print_error "Some components are missing. Please check the errors above."
fi

echo ""
print_info "For detailed information about the improvements, see:"
print_info "  📋 IMPROVEMENT_PLAN.md - Complete 10x improvement roadmap"
print_info "  🔬 DEEP_RESEARCH_FIX_SUMMARY.md - Deep research fixes and enhancements"